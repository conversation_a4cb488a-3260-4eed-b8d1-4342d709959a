{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, EventEmitter, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i5 from '@angular/material/select';\nimport { MatSelectModule } from '@angular/material/select';\nimport * as i7 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i6 from '@angular/material/core';\nimport { mixinDisabled, mixinInitialized } from '@angular/material/core';\nimport { coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i4 from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_div_2_mat_form_field_3_mat_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r6, \" \");\n  }\n}\nfunction MatPaginator_div_2_mat_form_field_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 16)(1, \"mat-select\", 17);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_div_2_mat_form_field_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7._changePageSize($event.value));\n    });\n    i0.ɵɵtemplate(2, MatPaginator_div_2_mat_form_field_3_mat_option_2_Template, 2, 2, \"mat-option\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r3._formFieldAppearance)(\"color\", ctx_r3.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r3.pageSize)(\"disabled\", ctx_r3.disabled)(\"aria-labelledby\", ctx_r3._pageSizeLabelId)(\"panelClass\", ctx_r3.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r3.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.pageSize);\n  }\n}\nfunction MatPaginator_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_div_2_mat_form_field_3_Template, 3, 8, \"mat-form-field\", 14);\n    i0.ɵɵtemplate(4, MatPaginator_div_2_div_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r0._pageSizeLabelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._displayedPageSizeOptions.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._displayedPageSizeOptions.length <= 1);\n  }\n}\nfunction MatPaginator_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MatPaginator_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.firstPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._previousButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function MatPaginator_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.lastPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r2._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r2._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r2._nextButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2._intl.lastPageLabel);\n  }\n}\nlet MatPaginatorIntl = /*#__PURE__*/(() => {\n  class MatPaginatorIntl {\n    constructor() {\n      /**\n       * Stream to emit from when labels are changed. Use this to notify components when the labels have\n       * changed after initialization.\n       */\n      this.changes = new Subject();\n      /** A label for the page size selector. */\n      this.itemsPerPageLabel = 'Items per page:';\n      /** A label for the button that increments the current page. */\n      this.nextPageLabel = 'Next page';\n      /** A label for the button that decrements the current page. */\n      this.previousPageLabel = 'Previous page';\n      /** A label for the button that moves to the first page. */\n      this.firstPageLabel = 'First page';\n      /** A label for the button that moves to the last page. */\n      this.lastPageLabel = 'Last page';\n      /** A label for the range of items within the current page and the length of the whole list. */\n      this.getRangeLabel = (page, pageSize, length) => {\n        if (length == 0 || pageSize == 0) {\n          return `0 of ${length}`;\n        }\n        length = Math.max(length, 0);\n        const startIndex = page * pageSize;\n        // If the start index exceeds the list length, do not try and fix the end index to the end.\n        const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n        return `${startIndex + 1} – ${endIndex} of ${length}`;\n      };\n    }\n    static {\n      this.ɵfac = function MatPaginatorIntl_Factory(t) {\n        return new (t || MatPaginatorIntl)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: MatPaginatorIntl,\n        factory: MatPaginatorIntl.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MatPaginatorIntl;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n// Boilerplate for applying mixins to _MatPaginatorBase.\n/** @docs-private */\nconst _MatPaginatorMixinBase = /*#__PURE__*/mixinDisabled( /*#__PURE__*/mixinInitialized(class {}));\n/**\n * Base class with all of the `MatPaginator` functionality.\n * @docs-private\n */\nlet _MatPaginatorBase = /*#__PURE__*/(() => {\n  class _MatPaginatorBase extends _MatPaginatorMixinBase {\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n      return this._pageIndex;\n    }\n    set pageIndex(value) {\n      this._pageIndex = Math.max(coerceNumberProperty(value), 0);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n      return this._length;\n    }\n    set length(value) {\n      this._length = coerceNumberProperty(value);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n      return this._pageSize;\n    }\n    set pageSize(value) {\n      this._pageSize = Math.max(coerceNumberProperty(value), 0);\n      this._updateDisplayedPageSizeOptions();\n    }\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n      return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n      this._pageSizeOptions = (value || []).map(p => coerceNumberProperty(p));\n      this._updateDisplayedPageSizeOptions();\n    }\n    /** Whether to hide the page size selection UI from the user. */\n    get hidePageSize() {\n      return this._hidePageSize;\n    }\n    set hidePageSize(value) {\n      this._hidePageSize = coerceBooleanProperty(value);\n    }\n    /** Whether to show the first/last buttons UI to the user. */\n    get showFirstLastButtons() {\n      return this._showFirstLastButtons;\n    }\n    set showFirstLastButtons(value) {\n      this._showFirstLastButtons = coerceBooleanProperty(value);\n    }\n    constructor(_intl, _changeDetectorRef, defaults) {\n      super();\n      this._intl = _intl;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._pageIndex = 0;\n      this._length = 0;\n      this._pageSizeOptions = [];\n      this._hidePageSize = false;\n      this._showFirstLastButtons = false;\n      /** Used to configure the underlying `MatSelect` inside the paginator. */\n      this.selectConfig = {};\n      /** Event emitted when the paginator changes the page size or page index. */\n      this.page = new EventEmitter();\n      this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n      if (defaults) {\n        const {\n          pageSize,\n          pageSizeOptions,\n          hidePageSize,\n          showFirstLastButtons\n        } = defaults;\n        if (pageSize != null) {\n          this._pageSize = pageSize;\n        }\n        if (pageSizeOptions != null) {\n          this._pageSizeOptions = pageSizeOptions;\n        }\n        if (hidePageSize != null) {\n          this._hidePageSize = hidePageSize;\n        }\n        if (showFirstLastButtons != null) {\n          this._showFirstLastButtons = showFirstLastButtons;\n        }\n      }\n    }\n    ngOnInit() {\n      this._initialized = true;\n      this._updateDisplayedPageSizeOptions();\n      this._markInitialized();\n    }\n    ngOnDestroy() {\n      this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n      if (!this.hasNextPage()) {\n        return;\n      }\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = this.pageIndex + 1;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n      if (!this.hasPreviousPage()) {\n        return;\n      }\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = this.pageIndex - 1;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n      // hasPreviousPage being false implies at the start\n      if (!this.hasPreviousPage()) {\n        return;\n      }\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = 0;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n      // hasNextPage being false implies at the end\n      if (!this.hasNextPage()) {\n        return;\n      }\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = this.getNumberOfPages() - 1;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n      return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n      const maxPageIndex = this.getNumberOfPages() - 1;\n      return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n      if (!this.pageSize) {\n        return 0;\n      }\n      return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n      // Current page needs to be updated to reflect the new page size. Navigate to the page\n      // containing the previous page's first item.\n      const startIndex = this.pageIndex * this.pageSize;\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n      this.pageSize = pageSize;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n      return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n      return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n      if (!this._initialized) {\n        return;\n      }\n      // If no page size is provided, use the first page size option or the default page size.\n      if (!this.pageSize) {\n        this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n      }\n      this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n      if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n        this._displayedPageSizeOptions.push(this.pageSize);\n      }\n      // Sort the numbers using a number-specific sort function.\n      this._displayedPageSizeOptions.sort((a, b) => a - b);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n      this.page.emit({\n        previousPageIndex,\n        pageIndex: this.pageIndex,\n        pageSize: this.pageSize,\n        length: this.length\n      });\n    }\n    static {\n      this.ɵfac = function _MatPaginatorBase_Factory(t) {\n        i0.ɵɵinvalidFactory();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: _MatPaginatorBase,\n        inputs: {\n          color: \"color\",\n          pageIndex: \"pageIndex\",\n          length: \"length\",\n          pageSize: \"pageSize\",\n          pageSizeOptions: \"pageSizeOptions\",\n          hidePageSize: \"hidePageSize\",\n          showFirstLastButtons: \"showFirstLastButtons\",\n          selectConfig: \"selectConfig\"\n        },\n        outputs: {\n          page: \"page\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return _MatPaginatorBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nlet MatPaginator = /*#__PURE__*/(() => {\n  class MatPaginator extends _MatPaginatorBase {\n    constructor(intl, changeDetectorRef, defaults) {\n      super(intl, changeDetectorRef, defaults);\n      /** ID for the DOM node containing the paginator's items per page label. */\n      this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n      this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    static {\n      this.ɵfac = function MatPaginator_Factory(t) {\n        return new (t || MatPaginator)(i0.ɵɵdirectiveInject(MatPaginatorIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_PAGINATOR_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatPaginator,\n        selectors: [[\"mat-paginator\"]],\n        hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n        inputs: {\n          disabled: \"disabled\"\n        },\n        exportAs: [\"matPaginator\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 14,\n        vars: 14,\n        consts: [[1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [\"class\", \"mat-mdc-paginator-page-size\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"class\", \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"class\", \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-page-size-label\", 3, \"id\"], [\"class\", \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\", 4, \"ngIf\"], [\"class\", \"mat-mdc-paginator-page-size-value\", 4, \"ngIf\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\", \"click\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n        template: function MatPaginator_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵtemplate(2, MatPaginator_div_2_Template, 5, 4, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(6, MatPaginator_button_6_Template, 3, 5, \"button\", 5);\n            i0.ɵɵelementStart(7, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n              return ctx.previousPage();\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(8, \"svg\", 7);\n            i0.ɵɵelement(9, \"path\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(10, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n              return ctx.nextPage();\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(11, \"svg\", 7);\n            i0.ɵɵelement(12, \"path\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(13, MatPaginator_button_13_Template, 3, 5, \"button\", 11);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.hidePageSize);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showFirstLastButtons);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._previousButtonsDisabled());\n            i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._nextButtonsDisabled());\n            i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.showFirstLastButtons);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i3.MatIconButton, i4.MatFormField, i5.MatSelect, i6.MatOption, i7.MatTooltip],\n        styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap-reverse;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatPaginator;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatPaginatorModule = /*#__PURE__*/(() => {\n  class MatPaginatorModule {\n    static {\n      this.ɵfac = function MatPaginatorModule_Factory(t) {\n        return new (t || MatPaginatorModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatPaginatorModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [MAT_PAGINATOR_INTL_PROVIDER],\n        imports: [CommonModule, MatButtonModule, MatSelectModule, MatTooltipModule]\n      });\n    }\n  }\n  return MatPaginatorModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent, _MatPaginatorBase };\n//# sourceMappingURL=paginator.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}