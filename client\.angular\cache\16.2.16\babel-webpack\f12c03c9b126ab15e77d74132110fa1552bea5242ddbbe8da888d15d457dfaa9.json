{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function take(count) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (++seen <= count) {\n        subscriber.next(value);\n        if (count <= seen) {\n          subscriber.complete();\n        }\n      }\n    }));\n  });\n}\n//# sourceMappingURL=take.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}