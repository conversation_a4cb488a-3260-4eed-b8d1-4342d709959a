{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ApiService = /*#__PURE__*/(() => {\n  class ApiService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n    }\n    /**\n     * Generic GET request\n     * @param endpoint API endpoint\n     * @param params Query parameters\n     * @param requiresAuth Whether the request requires authentication\n     * @returns Observable of response\n     */\n    get(endpoint, params, requiresAuth = true) {\n      const url = `${this.apiUrl}/${endpoint}`;\n      const options = this.buildRequestOptions(params, requiresAuth);\n      return this.http.get(url, {\n        headers: options.headers,\n        params: options.params\n      });\n    }\n    /**\n     * Generic POST request\n     * @param endpoint API endpoint\n     * @param body Request body\n     * @param requiresAuth Whether the request requires authentication\n     * @returns Observable of response\n     */\n    post(endpoint, body, requiresAuth = true) {\n      const url = `${this.apiUrl}/${endpoint}`;\n      const options = this.buildRequestOptions(null, requiresAuth);\n      return this.http.post(url, body, {\n        headers: options.headers,\n        params: options.params\n      });\n    }\n    /**\n     * Generic PUT request\n     * @param endpoint API endpoint\n     * @param body Request body\n     * @param requiresAuth Whether the request requires authentication\n     * @returns Observable of response\n     */\n    put(endpoint, body, requiresAuth = true) {\n      const url = `${this.apiUrl}/${endpoint}`;\n      const options = this.buildRequestOptions(null, requiresAuth);\n      return this.http.put(url, body, {\n        headers: options.headers,\n        params: options.params\n      });\n    }\n    /**\n     * Generic DELETE request\n     * @param endpoint API endpoint\n     * @param requiresAuth Whether the request requires authentication\n     * @returns Observable of response\n     */\n    delete(endpoint, requiresAuth = true) {\n      const url = `${this.apiUrl}/${endpoint}`;\n      const options = this.buildRequestOptions(null, requiresAuth);\n      return this.http.delete(url, {\n        headers: options.headers,\n        params: options.params\n      });\n    }\n    /**\n     * Build request options\n     * @param params Query parameters\n     * @param requiresAuth Whether the request requires authentication\n     * @returns Request options\n     */\n    buildRequestOptions(params, requiresAuth = true) {\n      let httpParams = new HttpParams();\n      let httpHeaders = new HttpHeaders({\n        'Content-Type': 'application/json'\n      });\n      // Add query parameters\n      if (params) {\n        Object.keys(params).forEach(key => {\n          if (params[key] !== null && params[key] !== undefined) {\n            httpParams = httpParams.append(key, params[key]);\n          }\n        });\n      }\n      // Add authorization header\n      if (requiresAuth) {\n        const token = localStorage.getItem('token');\n        if (token) {\n          httpHeaders = httpHeaders.append('Authorization', `Bearer ${token}`);\n        }\n      }\n      return {\n        headers: httpHeaders,\n        params: httpParams\n      };\n    }\n    static {\n      this.ɵfac = function ApiService_Factory(t) {\n        return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ApiService,\n        factory: ApiService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ApiService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}