{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, inject, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nfunction MatMenuItem__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 3);\n    i0.ɵɵelement(1, \"polygon\", 4);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"keydown\", function MatMenu_ng_template_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    })(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closed.emit(\"click\"));\n    })(\"@transformMenu.start\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4._onAnimationStart($event));\n    })(\"@transformMenu.done\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._onAnimationDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.panelId)(\"ngClass\", ctx_r0._classList)(\"@transformMenu\", ctx_r0._panelAnimationState);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0.ariaLabelledby || null)(\"aria-describedby\", ctx_r0.ariaDescribedby || null);\n  }\n}\nconst _c3 = [\"*\"];\nconst MAT_MENU_PANEL = /*#__PURE__*/new InjectionToken('MAT_MENU_PANEL');\n\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = /*#__PURE__*/mixinDisableRipple( /*#__PURE__*/mixinDisabled(class {}));\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nlet MatMenuItem = /*#__PURE__*/(() => {\n  class MatMenuItem extends _MatMenuItemBase {\n    constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n      super();\n      this._elementRef = _elementRef;\n      this._document = _document;\n      this._focusMonitor = _focusMonitor;\n      this._parentMenu = _parentMenu;\n      this._changeDetectorRef = _changeDetectorRef;\n      /** ARIA role for the menu item. */\n      this.role = 'menuitem';\n      /** Stream that emits when the menu item is hovered. */\n      this._hovered = new Subject();\n      /** Stream that emits when the menu item is focused. */\n      this._focused = new Subject();\n      /** Whether the menu item is highlighted. */\n      this._highlighted = false;\n      /** Whether the menu item acts as a trigger for a sub-menu. */\n      this._triggersSubmenu = false;\n      _parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n      if (this._focusMonitor && origin) {\n        this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n      } else {\n        this._getHostElement().focus(options);\n      }\n      this._focused.next(this);\n    }\n    ngAfterViewInit() {\n      if (this._focusMonitor) {\n        // Start monitoring the element, so it gets the appropriate focused classes. We want\n        // to show the focus style for menu items only when the focus was not caused by a\n        // mouse or touch interaction.\n        this._focusMonitor.monitor(this._elementRef, false);\n      }\n    }\n    ngOnDestroy() {\n      if (this._focusMonitor) {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n      }\n      if (this._parentMenu && this._parentMenu.removeItem) {\n        this._parentMenu.removeItem(this);\n      }\n      this._hovered.complete();\n      this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n      return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n      return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n      this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n      const clone = this._elementRef.nativeElement.cloneNode(true);\n      const icons = clone.querySelectorAll('mat-icon, .material-icons');\n      // Strip away icons, so they don't show up in the text.\n      for (let i = 0; i < icons.length; i++) {\n        icons[i].remove();\n      }\n      return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n      // We need to mark this for check for the case where the content is coming from a\n      // `matMenuContent` whose change detection tree is at the declaration position,\n      // not the insertion position. See #23175.\n      // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n      this._highlighted = isHighlighted;\n      this._changeDetectorRef?.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n      // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n      this._triggersSubmenu = triggersSubmenu;\n      this._changeDetectorRef?.markForCheck();\n    }\n    _hasFocus() {\n      return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static {\n      this.ɵfac = function MatMenuItem_Factory(t) {\n        return new (t || MatMenuItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatMenuItem,\n        selectors: [[\"\", \"mat-menu-item\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-mdc-focus-indicator\"],\n        hostVars: 8,\n        hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n              return ctx._checkDisabled($event);\n            })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n              return ctx._handleMouseEnter();\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n            i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          role: \"role\"\n        },\n        exportAs: [\"matMenuItem\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c0,\n        ngContentSelectors: _c2,\n        decls: 5,\n        vars: 3,\n        consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"class\", \"mat-mdc-menu-submenu-icon\", \"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n        template: function MatMenuItem_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵprojection(0);\n            i0.ɵɵelementStart(1, \"span\", 0);\n            i0.ɵɵprojection(2, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(3, \"div\", 1);\n            i0.ɵɵtemplate(4, MatMenuItem__svg_svg_4_Template, 2, 0, \"svg\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx._triggersSubmenu);\n          }\n        },\n        dependencies: [i2.NgIf, i3.MatRipple],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatMenuItem;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = /*#__PURE__*/new InjectionToken('MatMenuContent');\nlet _MatMenuContentBase = /*#__PURE__*/(() => {\n  class _MatMenuContentBase {\n    constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n      this._template = _template;\n      this._componentFactoryResolver = _componentFactoryResolver;\n      this._appRef = _appRef;\n      this._injector = _injector;\n      this._viewContainerRef = _viewContainerRef;\n      this._document = _document;\n      this._changeDetectorRef = _changeDetectorRef;\n      /** Emits when the menu content has been attached. */\n      this._attached = new Subject();\n    }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n      if (!this._portal) {\n        this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n      }\n      this.detach();\n      if (!this._outlet) {\n        this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n      }\n      const element = this._template.elementRef.nativeElement;\n      // Because we support opening the same menu from different triggers (which in turn have their\n      // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n      // risk it staying attached to a pane that's no longer in the DOM.\n      element.parentNode.insertBefore(this._outlet.outletElement, element);\n      // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n      // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n      // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n      // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n      // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n      // @breaking-change 9.0.0 Make change detector ref required\n      this._changeDetectorRef?.markForCheck();\n      this._portal.attach(this._outlet, context);\n      this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n      if (this._portal.isAttached) {\n        this._portal.detach();\n      }\n    }\n    ngOnDestroy() {\n      if (this._outlet) {\n        this._outlet.dispose();\n      }\n    }\n    static {\n      this.ɵfac = function _MatMenuContentBase_Factory(t) {\n        return new (t || _MatMenuContentBase)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: _MatMenuContentBase\n      });\n    }\n  }\n  return _MatMenuContentBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Menu content that will be rendered lazily once the menu is opened. */\nlet MatMenuContent = /*#__PURE__*/(() => {\n  class MatMenuContent extends _MatMenuContentBase {\n    static {\n      this.ɵfac = /* @__PURE__ */function () {\n        let ɵMatMenuContent_BaseFactory;\n        return function MatMenuContent_Factory(t) {\n          return (ɵMatMenuContent_BaseFactory || (ɵMatMenuContent_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuContent)))(t || MatMenuContent);\n        };\n      }();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatMenuContent,\n        selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_MENU_CONTENT,\n          useExisting: MatMenuContent\n        }]), i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatMenuContent;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: /*#__PURE__*/trigger('transformMenu', [/*#__PURE__*/state('void', /*#__PURE__*/style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), /*#__PURE__*/transition('void => enter', /*#__PURE__*/animate('120ms cubic-bezier(0, 0, 0.2, 1)', /*#__PURE__*/style({\n    opacity: 1,\n    transform: 'scale(1)'\n  }))), /*#__PURE__*/transition('* => void', /*#__PURE__*/animate('100ms 25ms linear', /*#__PURE__*/style({\n    opacity: 0\n  })))]),\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: /*#__PURE__*/trigger('fadeInItems', [\n  /*#__PURE__*/\n  // TODO(crisbeto): this is inside the `transformMenu`\n  // now. Remove next time we do breaking changes.\n  state('showing', /*#__PURE__*/style({\n    opacity: 1\n  })), /*#__PURE__*/transition('void => *', [/*#__PURE__*/style({\n    opacity: 0\n  }), /*#__PURE__*/animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Base class with all of the `MatMenu` functionality. */\nlet _MatMenuBase = /*#__PURE__*/(() => {\n  class _MatMenuBase {\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n      return this._xPosition;\n    }\n    set xPosition(value) {\n      if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuInvalidPositionX();\n      }\n      this._xPosition = value;\n      this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n      return this._yPosition;\n    }\n    set yPosition(value) {\n      if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuInvalidPositionY();\n      }\n      this._yPosition = value;\n      this.setPositionClasses();\n    }\n    /** Whether the menu should overlap its trigger. */\n    get overlapTrigger() {\n      return this._overlapTrigger;\n    }\n    set overlapTrigger(value) {\n      this._overlapTrigger = coerceBooleanProperty(value);\n    }\n    /** Whether the menu has a backdrop. */\n    get hasBackdrop() {\n      return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n      this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n      const previousPanelClass = this._previousPanelClass;\n      if (previousPanelClass && previousPanelClass.length) {\n        previousPanelClass.split(' ').forEach(className => {\n          this._classList[className] = false;\n        });\n      }\n      this._previousPanelClass = classes;\n      if (classes && classes.length) {\n        classes.split(' ').forEach(className => {\n          this._classList[className] = true;\n        });\n        this._elementRef.nativeElement.className = '';\n      }\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n      return this.panelClass;\n    }\n    set classList(classes) {\n      this.panelClass = classes;\n    }\n    constructor(_elementRef, _ngZone, defaultOptions,\n    // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n    _changeDetectorRef) {\n      this._elementRef = _elementRef;\n      this._ngZone = _ngZone;\n      this._changeDetectorRef = _changeDetectorRef;\n      /** Only the direct descendant menu items. */\n      this._directDescendantItems = new QueryList();\n      /** Config object to be passed into the menu's ngClass */\n      this._classList = {};\n      /** Current state of the panel animation. */\n      this._panelAnimationState = 'void';\n      /** Emits whenever an animation on the menu completes. */\n      this._animationDone = new Subject();\n      /** Event emitted when the menu is closed. */\n      this.closed = new EventEmitter();\n      /**\n       * Event emitted when the menu is closed.\n       * @deprecated Switch to `closed` instead\n       * @breaking-change 8.0.0\n       */\n      this.close = this.closed;\n      this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n      this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n      this._xPosition = defaultOptions.xPosition;\n      this._yPosition = defaultOptions.yPosition;\n      this.backdropClass = defaultOptions.backdropClass;\n      this._overlapTrigger = defaultOptions.overlapTrigger;\n      this._hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n      this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n      this._updateDirectDescendants();\n      this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n      this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n      // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n      // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n      // is internal and we know that it gets completed on destroy.\n      this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n      this._directDescendantItems.changes.subscribe(itemsList => {\n        // Move focus to another item, if the active item is removed from the list.\n        // We need to debounce the callback, because multiple items might be removed\n        // in quick succession.\n        const manager = this._keyManager;\n        if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n          const items = itemsList.toArray();\n          const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n          if (items[index] && !items[index].disabled) {\n            manager.setActiveItem(index);\n          } else {\n            manager.setNextItemActive();\n          }\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._directDescendantItems.destroy();\n      this.closed.complete();\n      this._firstItemFocusSubscription?.unsubscribe();\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n      // Coerce the `changes` property because Angular types it as `Observable<any>`\n      const itemChanges = this._directDescendantItems.changes;\n      return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) {}\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) {}\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      const manager = this._keyManager;\n      switch (keyCode) {\n        case ESCAPE:\n          if (!hasModifierKey(event)) {\n            event.preventDefault();\n            this.closed.emit('keydown');\n          }\n          break;\n        case LEFT_ARROW:\n          if (this.parentMenu && this.direction === 'ltr') {\n            this.closed.emit('keydown');\n          }\n          break;\n        case RIGHT_ARROW:\n          if (this.parentMenu && this.direction === 'rtl') {\n            this.closed.emit('keydown');\n          }\n          break;\n        default:\n          if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n            manager.setFocusOrigin('keyboard');\n          }\n          manager.onKeydown(event);\n          return;\n      }\n      // Don't allow the event to propagate if we've already handled it, or it may\n      // end up reaching other overlays that were opened earlier (see #22694).\n      event.stopPropagation();\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n      // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n      this._firstItemFocusSubscription?.unsubscribe();\n      this._firstItemFocusSubscription = this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n        let menuPanel = null;\n        if (this._directDescendantItems.length) {\n          // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n          // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n          // because the panel is inside an `ng-template`. We work around it by starting from one of\n          // the items and walking up the DOM.\n          menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n        }\n        // If an item in the menuPanel is already focused, avoid overriding the focus.\n        if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n          const manager = this._keyManager;\n          manager.setFocusOrigin(origin).setFirstItemActive();\n          // If there's no active item at this point, it means that all the items are disabled.\n          // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n          // give _some_ feedback to screen readers.\n          if (!manager.activeItem && menuPanel) {\n            menuPanel.focus();\n          }\n        }\n      });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n      this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * Sets the menu panel elevation.\n     * @param depth Number of parent menus that come before the menu.\n     */\n    setElevation(depth) {\n      // The elevation starts at the base and increases by one for each level.\n      // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n      const elevation = Math.min(this._baseElevation + depth, 24);\n      const newElevation = `${this._elevationPrefix}${elevation}`;\n      const customElevation = Object.keys(this._classList).find(className => {\n        return className.startsWith(this._elevationPrefix);\n      });\n      if (!customElevation || customElevation === this._previousElevation) {\n        if (this._previousElevation) {\n          this._classList[this._previousElevation] = false;\n        }\n        this._classList[newElevation] = true;\n        this._previousElevation = newElevation;\n      }\n    }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n      const classes = this._classList;\n      classes['mat-menu-before'] = posX === 'before';\n      classes['mat-menu-after'] = posX === 'after';\n      classes['mat-menu-above'] = posY === 'above';\n      classes['mat-menu-below'] = posY === 'below';\n      // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n      this._changeDetectorRef?.markForCheck();\n    }\n    /** Starts the enter animation. */\n    _startAnimation() {\n      // @breaking-change 8.0.0 Combine with _resetAnimation.\n      this._panelAnimationState = 'enter';\n    }\n    /** Resets the panel animation to its initial state. */\n    _resetAnimation() {\n      // @breaking-change 8.0.0 Combine with _startAnimation.\n      this._panelAnimationState = 'void';\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(event) {\n      this._animationDone.next(event);\n      this._isAnimating = false;\n    }\n    _onAnimationStart(event) {\n      this._isAnimating = true;\n      // Scroll the content element to the top as soon as the animation starts. This is necessary,\n      // because we move focus to the first item while it's still being animated, which can throw\n      // the browser off when it determines the scroll position. Alternatively we can move focus\n      // when the animation is done, however moving focus asynchronously will interrupt screen\n      // readers which are in the process of reading out the menu already. We take the `element`\n      // from the `event` since we can't use a `ViewChild` to access the pane.\n      if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n        event.element.scrollTop = 0;\n      }\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n      this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n        this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n        this._directDescendantItems.notifyOnChanges();\n      });\n    }\n    static {\n      this.ɵfac = function _MatMenuBase_Factory(t) {\n        return new (t || _MatMenuBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: _MatMenuBase,\n        contentQueries: function _MatMenuBase_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n          }\n        },\n        viewQuery: function _MatMenuBase_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(TemplateRef, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n          }\n        },\n        inputs: {\n          backdropClass: \"backdropClass\",\n          ariaLabel: [\"aria-label\", \"ariaLabel\"],\n          ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n          ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n          xPosition: \"xPosition\",\n          yPosition: \"yPosition\",\n          overlapTrigger: \"overlapTrigger\",\n          hasBackdrop: \"hasBackdrop\",\n          panelClass: [\"class\", \"panelClass\"],\n          classList: \"classList\"\n        },\n        outputs: {\n          closed: \"closed\",\n          close: \"close\"\n        }\n      });\n    }\n  }\n  return _MatMenuBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatMenu = /*#__PURE__*/(() => {\n  class MatMenu extends _MatMenuBase {\n    constructor(_elementRef, _ngZone, _defaultOptions, changeDetectorRef) {\n      super(_elementRef, _ngZone, _defaultOptions, changeDetectorRef);\n      this._elevationPrefix = 'mat-elevation-z';\n      this._baseElevation = 8;\n    }\n    static {\n      this.ɵfac = function MatMenu_Factory(t) {\n        return new (t || MatMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatMenu,\n        selectors: [[\"mat-menu\"]],\n        hostAttrs: [\"ngSkipHydration\", \"\"],\n        hostVars: 3,\n        hostBindings: function MatMenu_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          }\n        },\n        exportAs: [\"matMenu\"],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_MENU_PANEL,\n          useExisting: MatMenu\n        }]), i0.ɵɵInheritDefinitionFeature],\n        ngContentSelectors: _c3,\n        decls: 1,\n        vars: 0,\n        consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", \"mat-mdc-elevation-specific\", 3, \"id\", \"ngClass\", \"keydown\", \"click\"], [1, \"mat-mdc-menu-content\"]],\n        template: function MatMenu_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 6, \"ng-template\");\n          }\n        },\n        dependencies: [i2.NgClass],\n        styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{--mat-menu-container-shape:4px;min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{margin-right:16px}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:16px}.mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-mdc-menu-item.mat-mdc-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-mdc-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems]\n        },\n        changeDetection: 0\n      });\n    }\n  }\n  return MatMenu;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\nlet _MatMenuTriggerBase = /*#__PURE__*/(() => {\n  class _MatMenuTriggerBase {\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n      return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n      this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n      return this._menu;\n    }\n    set menu(menu) {\n      if (menu === this._menu) {\n        return;\n      }\n      this._menu = menu;\n      this._menuCloseSubscription.unsubscribe();\n      if (menu) {\n        if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatMenuRecursiveError();\n        }\n        this._menuCloseSubscription = menu.close.subscribe(reason => {\n          this._destroyMenu(reason);\n          // If a click closed the menu, we should close the entire chain of nested menus.\n          if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n            this._parentMaterialMenu.closed.emit(reason);\n          }\n        });\n      }\n      this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu,\n    // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n    // tslint:disable-next-line: lightweight-tokens\n    _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n      this._overlay = _overlay;\n      this._element = _element;\n      this._viewContainerRef = _viewContainerRef;\n      this._menuItemInstance = _menuItemInstance;\n      this._dir = _dir;\n      this._focusMonitor = _focusMonitor;\n      this._ngZone = _ngZone;\n      this._overlayRef = null;\n      this._menuOpen = false;\n      this._closingActionsSubscription = Subscription.EMPTY;\n      this._hoverSubscription = Subscription.EMPTY;\n      this._menuCloseSubscription = Subscription.EMPTY;\n      this._changeDetectorRef = inject(ChangeDetectorRef);\n      /**\n       * Handles touch start events on the trigger.\n       * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n       */\n      this._handleTouchStart = event => {\n        if (!isFakeTouchstartFromScreenReader(event)) {\n          this._openedBy = 'touch';\n        }\n      };\n      // Tracking input type is necessary so it's possible to only auto-focus\n      // the first item of the list when the menu is opened via the keyboard\n      this._openedBy = undefined;\n      /**\n       * Whether focus should be restored when the menu is closed.\n       * Note that disabling this option can have accessibility implications\n       * and it's up to you to manage focus, if you decide to turn it off.\n       */\n      this.restoreFocus = true;\n      /** Event emitted when the associated menu is opened. */\n      this.menuOpened = new EventEmitter();\n      /**\n       * Event emitted when the associated menu is opened.\n       * @deprecated Switch to `menuOpened` instead\n       * @breaking-change 8.0.0\n       */\n      // tslint:disable-next-line:no-output-on-prefix\n      this.onMenuOpen = this.menuOpened;\n      /** Event emitted when the associated menu is closed. */\n      this.menuClosed = new EventEmitter();\n      /**\n       * Event emitted when the associated menu is closed.\n       * @deprecated Switch to `menuClosed` instead\n       * @breaking-change 8.0.0\n       */\n      // tslint:disable-next-line:no-output-on-prefix\n      this.onMenuClose = this.menuClosed;\n      this._scrollStrategy = scrollStrategy;\n      this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n      _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    }\n    ngAfterContentInit() {\n      this._handleHover();\n    }\n    ngOnDestroy() {\n      if (this._overlayRef) {\n        this._overlayRef.dispose();\n        this._overlayRef = null;\n      }\n      this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n      this._menuCloseSubscription.unsubscribe();\n      this._closingActionsSubscription.unsubscribe();\n      this._hoverSubscription.unsubscribe();\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n      return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n      return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n      return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n      const menu = this.menu;\n      if (this._menuOpen || !menu) {\n        return;\n      }\n      const overlayRef = this._createOverlay(menu);\n      const overlayConfig = overlayRef.getConfig();\n      const positionStrategy = overlayConfig.positionStrategy;\n      this._setPosition(menu, positionStrategy);\n      overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n      overlayRef.attach(this._getPortal(menu));\n      if (menu.lazyContent) {\n        menu.lazyContent.attach(this.menuData);\n      }\n      this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n      this._initMenu(menu);\n      if (menu instanceof _MatMenuBase) {\n        menu._startAnimation();\n        menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n          // Re-adjust the position without locking when the amount of items\n          // changes so that the overlay is allowed to pick a new optimal position.\n          positionStrategy.withLockedPosition(false).reapplyLastPosition();\n          positionStrategy.withLockedPosition(true);\n        });\n      }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n      this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n      if (this._focusMonitor && origin) {\n        this._focusMonitor.focusVia(this._element, origin, options);\n      } else {\n        this._element.nativeElement.focus(options);\n      }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n      this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n      if (!this._overlayRef || !this.menuOpen) {\n        return;\n      }\n      const menu = this.menu;\n      this._closingActionsSubscription.unsubscribe();\n      this._overlayRef.detach();\n      // Always restore focus if the user is navigating using the keyboard or the menu was opened\n      // programmatically. We don't restore for non-root triggers, because it can prevent focus\n      // from making it back to the root trigger when closing a long chain of menus by clicking\n      // on the backdrop.\n      if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n        this.focus(this._openedBy);\n      }\n      this._openedBy = undefined;\n      if (menu instanceof _MatMenuBase) {\n        menu._resetAnimation();\n        if (menu.lazyContent) {\n          // Wait for the exit animation to finish before detaching the content.\n          menu._animationDone.pipe(filter(event => event.toState === 'void'), take(1),\n          // Interrupt if the content got re-attached.\n          takeUntil(menu.lazyContent._attached)).subscribe({\n            next: () => menu.lazyContent.detach(),\n            // No matter whether the content got re-attached, reset the menu.\n            complete: () => this._setIsMenuOpen(false)\n          });\n        } else {\n          this._setIsMenuOpen(false);\n        }\n      } else {\n        this._setIsMenuOpen(false);\n        menu?.lazyContent?.detach();\n      }\n    }\n    /**\n     * This method sets the menu state to open and focuses the first item if\n     * the menu was opened via the keyboard.\n     */\n    _initMenu(menu) {\n      menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n      menu.direction = this.dir;\n      this._setMenuElevation(menu);\n      menu.focusFirstItem(this._openedBy || 'program');\n      this._setIsMenuOpen(true);\n    }\n    /** Updates the menu elevation based on the amount of parent menus that it has. */\n    _setMenuElevation(menu) {\n      if (menu.setElevation) {\n        let depth = 0;\n        let parentMenu = menu.parentMenu;\n        while (parentMenu) {\n          depth++;\n          parentMenu = parentMenu.parentMenu;\n        }\n        menu.setElevation(depth);\n      }\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n      if (isOpen !== this._menuOpen) {\n        this._menuOpen = isOpen;\n        this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n        if (this.triggersSubmenu()) {\n          this._menuItemInstance._setHighlighted(isOpen);\n        }\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n      if (!this._overlayRef) {\n        const config = this._getOverlayConfig(menu);\n        this._subscribeToPositions(menu, config.positionStrategy);\n        this._overlayRef = this._overlay.create(config);\n        // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n        // Ideally we'd also have our keyboard event logic in here, however doing so will\n        // break anybody that may have implemented the `MatMenuPanel` themselves.\n        this._overlayRef.keydownEvents().subscribe();\n      }\n      return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n      return new OverlayConfig({\n        positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n        backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n        panelClass: menu.overlayPanelClass,\n        scrollStrategy: this._scrollStrategy(),\n        direction: this._dir\n      });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n      if (menu.setPositionClasses) {\n        position.positionChanges.subscribe(change => {\n          const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n          const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n          // @breaking-change 15.0.0 Remove null check for `ngZone`.\n          // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n          // updating something in the view so we need to bring it back in.\n          if (this._ngZone) {\n            this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n          } else {\n            menu.setPositionClasses(posX, posY);\n          }\n        });\n      }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n      let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n      let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n      let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n      let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n      let offsetY = 0;\n      if (this.triggersSubmenu()) {\n        // When the menu is a sub-menu, it should always align itself\n        // to the edges of the trigger, instead of overlapping it.\n        overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n        originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n        if (this._parentMaterialMenu) {\n          if (this._parentInnerPadding == null) {\n            const firstItem = this._parentMaterialMenu.items.first;\n            this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n          }\n          offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n        }\n      } else if (!menu.overlapTrigger) {\n        originY = overlayY === 'top' ? 'bottom' : 'top';\n        originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n      }\n      positionStrategy.withPositions([{\n        originX,\n        originY,\n        overlayX,\n        overlayY,\n        offsetY\n      }, {\n        originX: originFallbackX,\n        originY,\n        overlayX: overlayFallbackX,\n        overlayY,\n        offsetY\n      }, {\n        originX,\n        originY: originFallbackY,\n        overlayX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY\n      }, {\n        originX: originFallbackX,\n        originY: originFallbackY,\n        overlayX: overlayFallbackX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY\n      }]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n      const backdrop = this._overlayRef.backdropClick();\n      const detachments = this._overlayRef.detachments();\n      const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n      const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen)) : of();\n      return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n      if (!isFakeMousedownFromScreenReader(event)) {\n        // Since right or middle button clicks won't trigger the `click` event,\n        // we shouldn't consider the menu as opened by mouse in those cases.\n        this._openedBy = event.button === 0 ? 'mouse' : undefined;\n        // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n        // we should prevent focus from moving onto it via click to avoid the\n        // highlight from lingering on the menu item.\n        if (this.triggersSubmenu()) {\n          event.preventDefault();\n        }\n      }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      // Pressing enter on the trigger will trigger the click handler later.\n      if (keyCode === ENTER || keyCode === SPACE) {\n        this._openedBy = 'keyboard';\n      }\n      if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n        this._openedBy = 'keyboard';\n        this.openMenu();\n      }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n      if (this.triggersSubmenu()) {\n        // Stop event propagation to avoid closing the parent menu.\n        event.stopPropagation();\n        this.openMenu();\n      } else {\n        this.toggleMenu();\n      }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n      // Subscribe to changes in the hovered item in order to toggle the panel.\n      if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n        return;\n      }\n      this._hoverSubscription = this._parentMaterialMenu._hovered()\n      // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n      // with different data and triggers), we have to delay it by a tick to ensure that\n      // it won't be closed immediately after it is opened.\n      .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler)).subscribe(() => {\n        this._openedBy = 'mouse';\n        // If the same menu is used between multiple triggers, it might still be animating\n        // while the new trigger tries to re-open it. Wait for the animation to finish\n        // before doing so. Also interrupt if the user moves to another item.\n        if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n          // We need the `delay(0)` here in order to avoid\n          // 'changed after checked' errors in some cases. See #12194.\n          this.menu._animationDone.pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered())).subscribe(() => this.openMenu());\n        } else {\n          this.openMenu();\n        }\n      });\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n      // Note that we can avoid this check by keeping the portal on the menu panel.\n      // While it would be cleaner, we'd have to introduce another required method on\n      // `MatMenuPanel`, making it harder to consume.\n      if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n        this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n      }\n      return this._portal;\n    }\n    static {\n      this.ɵfac = function _MatMenuTriggerBase_Factory(t) {\n        return new (t || _MatMenuTriggerBase)(i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_MENU_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(MatMenuItem, 10), i0.ɵɵdirectiveInject(i3$1.Directionality, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: _MatMenuTriggerBase,\n        hostVars: 3,\n        hostBindings: function _MatMenuTriggerBase_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function _MatMenuTriggerBase_click_HostBindingHandler($event) {\n              return ctx._handleClick($event);\n            })(\"mousedown\", function _MatMenuTriggerBase_mousedown_HostBindingHandler($event) {\n              return ctx._handleMousedown($event);\n            })(\"keydown\", function _MatMenuTriggerBase_keydown_HostBindingHandler($event) {\n              return ctx._handleKeydown($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n          }\n        },\n        inputs: {\n          _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n          menu: [\"matMenuTriggerFor\", \"menu\"],\n          menuData: [\"matMenuTriggerData\", \"menuData\"],\n          restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n        },\n        outputs: {\n          menuOpened: \"menuOpened\",\n          onMenuOpen: \"onMenuOpen\",\n          menuClosed: \"menuClosed\",\n          onMenuClose: \"onMenuClose\"\n        }\n      });\n    }\n  }\n  return _MatMenuTriggerBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nlet MatMenuTrigger = /*#__PURE__*/(() => {\n  class MatMenuTrigger extends _MatMenuTriggerBase {\n    static {\n      this.ɵfac = /* @__PURE__ */function () {\n        let ɵMatMenuTrigger_BaseFactory;\n        return function MatMenuTrigger_Factory(t) {\n          return (ɵMatMenuTrigger_BaseFactory || (ɵMatMenuTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuTrigger)))(t || MatMenuTrigger);\n        };\n      }();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatMenuTrigger,\n        selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n        exportAs: [\"matMenuTrigger\"],\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatMenuTrigger;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatMenuModule = /*#__PURE__*/(() => {\n  class MatMenuModule {\n    static {\n      this.ɵfac = function MatMenuModule_Factory(t) {\n        return new (t || MatMenuModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatMenuModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n        imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n      });\n    }\n  }\n  return MatMenuModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };\n//# sourceMappingURL=menu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}