{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport let JobService = /*#__PURE__*/(() => {\n  class JobService {\n    constructor(apiService) {\n      this.apiService = apiService;\n    }\n    /**\n     * Get all jobs with filtering\n     * @param params Query parameters\n     * @returns Observable of jobs response\n     */\n    getJobs(params) {\n      return this.apiService.get('jobs', params, false);\n    }\n    /**\n     * Get job by ID\n     * @param id Job ID\n     * @returns Observable of job response\n     */\n    getJobById(id) {\n      return this.apiService.get(`jobs/${id}`, null, false);\n    }\n    /**\n     * Create a new job\n     * @param jobRequest Job request\n     * @returns Observable of job response\n     */\n    createJob(jobRequest) {\n      return this.apiService.post('jobs', jobRequest);\n    }\n    /**\n     * Update job\n     * @param id Job ID\n     * @param jobRequest Job request\n     * @returns Observable of job response\n     */\n    updateJob(id, jobRequest) {\n      return this.apiService.put(`jobs/${id}`, jobRequest);\n    }\n    /**\n     * Delete job\n     * @param id Job ID\n     * @returns Observable of response\n     */\n    deleteJob(id) {\n      return this.apiService.delete(`jobs/${id}`);\n    }\n    /**\n     * Get jobs posted by the current employer\n     * @param params Query parameters\n     * @returns Observable of jobs response\n     */\n    getEmployerJobs(params) {\n      return this.apiService.get('jobs/employer/me', params);\n    }\n    static {\n      this.ɵfac = function JobService_Factory(t) {\n        return new (t || JobService)(i0.ɵɵinject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: JobService,\n        factory: JobService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return JobService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}