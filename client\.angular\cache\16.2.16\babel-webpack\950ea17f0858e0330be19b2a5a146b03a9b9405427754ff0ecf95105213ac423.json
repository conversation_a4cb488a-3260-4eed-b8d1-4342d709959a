{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"First name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Last name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Account type is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_mat_spinner_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction RegisterComponent_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Register\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(formBuilder, authService, router, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.hidePassword = true;\n    }\n    ngOnInit() {\n      // Initialize form\n      this.registerForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        role: ['job_seeker', Validators.required],\n        firstName: ['', Validators.required],\n        lastName: ['', Validators.required]\n      });\n      // Redirect if already logged in\n      if (this.authService.checkAuth()) {\n        this.router.navigate(['/']);\n      }\n    }\n    // Convenience getter for easy access to form fields\n    get f() {\n      return this.registerForm.controls;\n    }\n    onSubmit() {\n      // Stop here if form is invalid\n      if (this.registerForm.invalid) {\n        return;\n      }\n      this.loading = true;\n      const registerRequest = {\n        email: this.f['email'].value,\n        password: this.f['password'].value,\n        role: this.f['role'].value,\n        firstName: this.f['firstName'].value,\n        lastName: this.f['lastName'].value\n      };\n      this.authService.register(registerRequest).subscribe({\n        next: () => {\n          this.snackBar.open('Registration successful', 'Close', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          this.router.navigate(['/']);\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Registration failed', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function RegisterComponent_Factory(t) {\n        return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegisterComponent,\n        selectors: [[\"app-register\"]],\n        decls: 50,\n        vars: 13,\n        consts: [[1, \"register-container\"], [1, \"register-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"required\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"required\", \"\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", \"required\", \"\"], [\"matNativeControl\", \"\", \"formControlName\", \"role\", \"required\", \"\"], [\"value\", \"job_seeker\"], [\"value\", \"employer\"], [1, \"button-container\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"routerLink\", \"/login\"], [\"diameter\", \"20\"]],\n        template: function RegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"Create an Account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"form\", 2);\n            i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_6_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(7, \"mat-form-field\", 3)(8, \"mat-label\");\n            i0.ɵɵtext(9, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 4);\n            i0.ɵɵtemplate(11, RegisterComponent_mat_error_11_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵtemplate(12, RegisterComponent_mat_error_12_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-form-field\", 3)(14, \"mat-label\");\n            i0.ɵɵtext(15, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"input\", 6);\n            i0.ɵɵelementStart(17, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_17_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(18, \"mat-icon\");\n            i0.ɵɵtext(19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(20, RegisterComponent_mat_error_20_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵtemplate(21, RegisterComponent_mat_error_21_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"mat-form-field\", 3)(23, \"mat-label\");\n            i0.ɵɵtext(24, \"First Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 8);\n            i0.ɵɵtemplate(26, RegisterComponent_mat_error_26_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"mat-form-field\", 3)(28, \"mat-label\");\n            i0.ɵɵtext(29, \"Last Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 9);\n            i0.ɵɵtemplate(31, RegisterComponent_mat_error_31_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"mat-form-field\", 3)(33, \"mat-label\");\n            i0.ɵɵtext(34, \"Account Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"select\", 10)(36, \"option\", 11);\n            i0.ɵɵtext(37, \"Job Seeker\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"option\", 12);\n            i0.ɵɵtext(39, \"Employer\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(40, RegisterComponent_mat_error_40_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 13)(42, \"button\", 14);\n            i0.ɵɵtemplate(43, RegisterComponent_mat_spinner_43_Template, 1, 0, \"mat-spinner\", 15);\n            i0.ɵɵtemplate(44, RegisterComponent_span_44_Template, 2, 0, \"span\", 5);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(45, \"mat-card-actions\")(46, \"p\");\n            i0.ɵɵtext(47, \"Already have an account? \");\n            i0.ɵɵelementStart(48, \"a\", 16);\n            i0.ɵɵtext(49, \"Login\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"email\"].errors == null ? null : ctx.f[\"email\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"email\"].errors == null ? null : ctx.f[\"email\"].errors[\"email\"]);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"minlength\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"firstName\"].errors == null ? null : ctx.f[\"firstName\"].errors[\"required\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"lastName\"].errors == null ? null : ctx.f[\"lastName\"].errors[\"required\"]);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"role\"].errors == null ? null : ctx.f[\"role\"].errors[\"required\"]);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i5.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatIcon, i11.MatProgressSpinner],\n        styles: [\".register-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:calc(100vh - 64px);padding:20px}.register-card[_ngcontent-%COMP%]{max-width:500px;width:100%}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:15px}.button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:20px;margin-bottom:10px}mat-card-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:0 16px 16px}mat-spinner[_ngcontent-%COMP%]{display:inline-block;margin-right:5px}a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}a[_ngcontent-%COMP%]:hover{text-decoration:underline}\"]\n      });\n    }\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}