{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { JobListComponent } from './components/job-list/job-list.component';\nimport { JobDetailComponent } from './components/job-detail/job-detail.component';\nimport { JobCreateComponent } from './components/job-create/job-create.component';\nimport { ApplicationListComponent } from './components/application-list/application-list.component';\nimport { ProfileComponent } from './components/profile/profile.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport { EmployerGuard } from './guards/employer.guard';\nimport { JobSeekerGuard } from './guards/job-seeker.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/jobs',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'jobs',\n  component: JobListComponent\n}, {\n  path: 'jobs/:id',\n  component: JobDetailComponent\n}, {\n  path: 'create-job',\n  component: JobCreateComponent,\n  canActivate: [AuthGuard, EmployerGuard]\n}, {\n  path: 'my-jobs',\n  component: JobListComponent,\n  canActivate: [AuthGuard, EmployerGuard],\n  data: {\n    employerJobs: true\n  }\n}, {\n  path: 'my-applications',\n  component: ApplicationListComponent,\n  canActivate: [AuthGuard, JobSeekerGuard]\n}, {\n  path: 'profile',\n  component: ProfileComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/jobs'\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(t) {\n        return new (t || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}