{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/job.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"../job-form/job-form.component\";\nfunction JobCreateComponent_h1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\");\n    i0.ɵɵtext(1, \"Post a New Job\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobCreateComponent_h1_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\");\n    i0.ɵɵtext(1, \"Edit Job\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobCreateComponent_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Fill out the form below to create a new job listing.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobCreateComponent_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Update the information for this job listing.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let JobCreateComponent = /*#__PURE__*/(() => {\n  class JobCreateComponent {\n    constructor(jobService, authService, router, route, snackBar) {\n      this.jobService = jobService;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.job = null;\n    }\n    ngOnInit() {\n      const id = this.route.snapshot.queryParamMap.get('id');\n      if (id) {\n        this.jobId = +id;\n        this.loadJobData();\n      }\n      // Check if user is authenticated and is an employer\n      if (!this.authService.checkAuth() || !this.authService.isEmployer()) {\n        this.snackBar.open('Only employers can create jobs', 'Close', {\n          duration: 5000\n        });\n        this.router.navigate(['/jobs']);\n      }\n    }\n    loadJobData() {\n      this.loading = true;\n      this.jobService.getJobById(this.jobId).subscribe({\n        next: response => {\n          this.job = response.data.job;\n          this.loading = false;\n          console.log('Job loaded:', this.job);\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error loading job details', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n          this.router.navigate(['/jobs']);\n        }\n      });\n    }\n    onSubmit(jobRequest) {\n      this.loading = true;\n      // Determine if we're creating a new job or updating an existing one\n      if (this.jobId) {\n        // Update existing job\n        this.jobService.updateJob(this.jobId, jobRequest).subscribe({\n          next: response => {\n            this.snackBar.open('Job updated successfully!', 'Close', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            this.loading = false;\n            // Navigate to the updated job\n            if (response.data.job.id) {\n              this.router.navigate(['/jobs', response.data.job.id]);\n            } else {\n              this.router.navigate(['/my-jobs']);\n            }\n          },\n          error: error => {\n            this.snackBar.open(error.error?.message || 'Error updating job', 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n            this.loading = false;\n          }\n        });\n      } else {\n        // Create new job\n        this.jobService.createJob(jobRequest).subscribe({\n          next: response => {\n            this.snackBar.open('Job created successfully!', 'Close', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            this.loading = false;\n            // Navigate to the newly created job\n            if (response.data.job.id) {\n              this.router.navigate(['/jobs', response.data.job.id]);\n            } else {\n              this.router.navigate(['/my-jobs']);\n            }\n          },\n          error: error => {\n            this.snackBar.open(error.error?.message || 'Error creating job', 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n            this.loading = false;\n          }\n        });\n      }\n    }\n    onCancel() {\n      this.router.navigate(['/my-jobs']);\n    }\n    static {\n      this.ɵfac = function JobCreateComponent_Factory(t) {\n        return new (t || JobCreateComponent)(i0.ɵɵdirectiveInject(i1.JobService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: JobCreateComponent,\n        selectors: [[\"app-job-create\"]],\n        decls: 9,\n        vars: 6,\n        consts: [[1, \"job-create-container\"], [1, \"job-create-header\"], [4, \"ngIf\"], [1, \"job-create-card\"], [3, \"loading\", \"job\", \"formSubmit\", \"formCancel\"]],\n        template: function JobCreateComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵtemplate(2, JobCreateComponent_h1_2_Template, 2, 0, \"h1\", 2);\n            i0.ɵɵtemplate(3, JobCreateComponent_h1_3_Template, 2, 0, \"h1\", 2);\n            i0.ɵɵtemplate(4, JobCreateComponent_p_4_Template, 2, 0, \"p\", 2);\n            i0.ɵɵtemplate(5, JobCreateComponent_p_5_Template, 2, 0, \"p\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"mat-card\", 3)(7, \"mat-card-content\")(8, \"app-job-form\", 4);\n            i0.ɵɵlistener(\"formSubmit\", function JobCreateComponent_Template_app_job_form_formSubmit_8_listener($event) {\n              return ctx.onSubmit($event);\n            })(\"formCancel\", function JobCreateComponent_Template_app_job_form_formCancel_8_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.jobId);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.jobId);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.jobId);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.jobId);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"loading\", ctx.loading)(\"job\", ctx.job);\n          }\n        },\n        dependencies: [i5.NgIf, i6.MatCard, i6.MatCardContent, i7.JobFormComponent],\n        styles: [\".job-create-container[_ngcontent-%COMP%]{margin-bottom:30px}.job-create-header[_ngcontent-%COMP%]{margin-bottom:20px}.job-create-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin-bottom:8px;font-weight:500}.job-create-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin:0}.job-create-card[_ngcontent-%COMP%]{margin-bottom:20px}\"]\n      });\n    }\n  }\n  return JobCreateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}