{"ast": null, "code": "import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}\n//# sourceMappingURL=partition.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}