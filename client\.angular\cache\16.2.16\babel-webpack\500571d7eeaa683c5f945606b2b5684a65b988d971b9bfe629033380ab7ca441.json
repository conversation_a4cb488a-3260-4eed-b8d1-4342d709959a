{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport let ApplicationService = /*#__PURE__*/(() => {\n  class ApplicationService {\n    constructor(apiService) {\n      this.apiService = apiService;\n    }\n    /**\n     * Apply for a job\n     * @param applicationRequest Application request\n     * @returns Observable of application response\n     */\n    applyForJob(applicationRequest) {\n      return this.apiService.post('applications', applicationRequest);\n    }\n    /**\n     * Get applications for a job (employer only)\n     * @param jobId Job ID\n     * @param params Query parameters\n     * @returns Observable of applications response\n     */\n    getJobApplications(jobId, params) {\n      return this.apiService.get(`applications/job/${jobId}`, params);\n    }\n    /**\n     * Update application status (employer only)\n     * @param id Application ID\n     * @param statusRequest Status request\n     * @returns Observable of application response\n     */\n    updateApplicationStatus(id, statusRequest) {\n      return this.apiService.put(`applications/${id}/status`, statusRequest);\n    }\n    /**\n     * Get user's applications (job seeker only)\n     * @param params Query parameters\n     * @returns Observable of applications response\n     */\n    getUserApplications(params) {\n      return this.apiService.get('applications/me', params);\n    }\n    /**\n     * Get application by ID\n     * @param id Application ID\n     * @returns Observable of application response\n     */\n    getApplicationById(id) {\n      return this.apiService.get(`applications/${id}`);\n    }\n    static {\n      this.ɵfac = function ApplicationService_Factory(t) {\n        return new (t || ApplicationService)(i0.ɵɵinject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ApplicationService,\n        factory: ApplicationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ApplicationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}