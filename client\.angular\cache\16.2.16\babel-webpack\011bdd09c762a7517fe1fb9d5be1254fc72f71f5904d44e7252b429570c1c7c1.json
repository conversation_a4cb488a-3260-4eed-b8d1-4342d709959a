{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/profile.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction ProfileComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name cannot exceed 50 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Last name cannot exceed 50 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Phone number cannot exceed 20 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Address cannot exceed 100 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_29_mat_error_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Resume URL cannot exceed 255 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h2\");\n    i0.ɵɵtext(2, \"Job Seeker Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 23)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"Resume URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 24);\n    i0.ɵɵtemplate(7, ProfileComponent_div_7_div_29_mat_error_7_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.profileForm.get(\"resumeUrl\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction ProfileComponent_div_7_div_30_mat_error_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Company name cannot exceed 100 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_30_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Company description cannot exceed 1000 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h2\");\n    i0.ɵɵtext(2, \"Company Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-form-field\", 23)(4, \"mat-label\");\n    i0.ɵɵtext(5, \"Company Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 25);\n    i0.ɵɵtemplate(7, ProfileComponent_div_7_div_30_mat_error_7_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 23)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Company Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"textarea\", 26);\n    i0.ɵɵtemplate(12, ProfileComponent_div_7_div_30_mat_error_12_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r7.profileForm.get(\"companyName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r7.profileForm.get(\"companyDescription\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction ProfileComponent_div_7_mat_spinner_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction ProfileComponent_div_7_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Profile\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"mat-card\", 7)(2, \"mat-card-content\")(3, \"form\", 8);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_7_Template_form_ngSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onSubmit());\n    });\n    i0.ɵɵelementStart(4, \"div\", 9)(5, \"h2\");\n    i0.ɵɵtext(6, \"Personal Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"mat-form-field\", 11)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 12);\n    i0.ɵɵtemplate(12, ProfileComponent_div_7_mat_error_12_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 11)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 14);\n    i0.ɵɵtemplate(17, ProfileComponent_div_7_mat_error_17_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"mat-form-field\", 11)(20, \"mat-label\");\n    i0.ɵɵtext(21, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 15);\n    i0.ɵɵtemplate(23, ProfileComponent_div_7_mat_error_23_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-form-field\", 11)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 16);\n    i0.ɵɵtemplate(28, ProfileComponent_div_7_mat_error_28_Template, 2, 0, \"mat-error\", 13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(29, ProfileComponent_div_7_div_29_Template, 8, 1, \"div\", 17);\n    i0.ɵɵtemplate(30, ProfileComponent_div_7_div_30_Template, 13, 2, \"div\", 17);\n    i0.ɵɵelementStart(31, \"div\", 18)(32, \"button\", 19);\n    i0.ɵɵtemplate(33, ProfileComponent_div_7_mat_spinner_33_Template, 1, 0, \"mat-spinner\", 20);\n    i0.ɵɵtemplate(34, ProfileComponent_div_7_span_34_Template, 2, 0, \"span\", 13);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 21)(36, \"mat-card-header\")(37, \"mat-card-title\");\n    i0.ɵɵtext(38, \"Account Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"mat-card-content\")(40, \"div\", 22)(41, \"p\")(42, \"strong\");\n    i0.ɵɵtext(43, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\")(46, \"strong\");\n    i0.ɵɵtext(47, \"Role:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.profileForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.profileForm.get(\"lastName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.profileForm.get(\"phone\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.profileForm.get(\"address\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"maxlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJobSeeker());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEmployer());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.profileForm.invalid || ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.saving);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user == null ? null : ctx_r1.user.email, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.user == null ? null : ctx_r1.user.role) === \"job_seeker\" ? \"Job Seeker\" : \"Employer\", \"\");\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(formBuilder, authService, profileService, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.profileService = profileService;\n      this.snackBar = snackBar;\n      this.user = null;\n      this.profile = null;\n      this.loading = false;\n      this.saving = false;\n    }\n    ngOnInit() {\n      this.initForm();\n      this.loadUserProfile();\n    }\n    initForm() {\n      this.profileForm = this.formBuilder.group({\n        // Common fields\n        firstName: ['', [Validators.maxLength(50)]],\n        lastName: ['', [Validators.maxLength(50)]],\n        phone: ['', [Validators.maxLength(20)]],\n        address: ['', [Validators.maxLength(100)]],\n        // Job seeker fields\n        resumeUrl: ['', [Validators.maxLength(255)]],\n        // Employer fields\n        companyName: ['', [Validators.maxLength(100)]],\n        companyDescription: ['', [Validators.maxLength(1000)]]\n      });\n    }\n    loadUserProfile() {\n      this.loading = true;\n      // Get current user from local storage\n      const userJson = localStorage.getItem('user');\n      if (userJson) {\n        this.user = JSON.parse(userJson);\n      }\n      // Get profile\n      this.profileService.getProfile().subscribe({\n        next: response => {\n          this.profile = response.data.profile;\n          this.updateFormWithProfileData();\n          this.loading = false;\n        },\n        error: error => {\n          // If profile doesn't exist yet, that's okay\n          if (error.status !== 404) {\n            this.snackBar.open(error.error?.message || 'Error loading profile', 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n          this.loading = false;\n        }\n      });\n    }\n    updateFormWithProfileData() {\n      if (!this.profile) return;\n      this.profileForm.patchValue({\n        firstName: this.profile.firstName || '',\n        lastName: this.profile.lastName || '',\n        phone: this.profile.phone || '',\n        address: this.profile.address || '',\n        resumeUrl: this.profile.resumeUrl || '',\n        companyName: this.profile.companyName || '',\n        companyDescription: this.profile.companyDescription || ''\n      });\n    }\n    onSubmit() {\n      if (this.profileForm.invalid) {\n        return;\n      }\n      this.saving = true;\n      const profileData = {\n        firstName: this.profileForm.get('firstName')?.value,\n        lastName: this.profileForm.get('lastName')?.value,\n        phone: this.profileForm.get('phone')?.value,\n        address: this.profileForm.get('address')?.value\n      };\n      // Add role-specific fields\n      if (this.isJobSeeker()) {\n        profileData.resumeUrl = this.profileForm.get('resumeUrl')?.value;\n      } else if (this.isEmployer()) {\n        profileData.companyName = this.profileForm.get('companyName')?.value;\n        profileData.companyDescription = this.profileForm.get('companyDescription')?.value;\n      }\n      this.profileService.updateProfile(profileData).subscribe({\n        next: response => {\n          this.profile = response.data.profile;\n          this.snackBar.open('Profile updated successfully!', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          this.saving = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error updating profile', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.saving = false;\n        }\n      });\n    }\n    isJobSeeker() {\n      return this.user?.role === 'job_seeker';\n    }\n    isEmployer() {\n      return this.user?.role === 'employer';\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ProfileService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        decls: 8,\n        vars: 2,\n        consts: [[1, \"profile-container\"], [1, \"profile-header\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"profile-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"profile-content\"], [1, \"profile-card\"], [1, \"profile-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"placeholder\", \"Enter your phone number\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\"], [\"class\", \"form-section\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"account-card\"], [1, \"account-info\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"resumeUrl\", \"placeholder\", \"Enter URL to your resume\"], [\"matInput\", \"\", \"formControlName\", \"companyName\", \"placeholder\", \"Enter your company name\"], [\"matInput\", \"\", \"formControlName\", \"companyDescription\", \"placeholder\", \"Enter a description of your company\", \"rows\", \"4\"], [\"diameter\", \"20\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"My Profile\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Manage your personal information and preferences.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, ProfileComponent_div_6_Template, 2, 0, \"div\", 2);\n            i0.ɵɵtemplate(7, ProfileComponent_div_7_Template, 49, 12, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i10.MatProgressSpinner],\n        styles: [\".profile-container[_ngcontent-%COMP%]{margin-bottom:30px}.profile-header[_ngcontent-%COMP%]{margin-bottom:20px}.profile-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin-bottom:8px;font-weight:500}.profile-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin:0}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:40px 0}.profile-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr;gap:20px}.profile-card[_ngcontent-%COMP%], .account-card[_ngcontent-%COMP%]{margin-bottom:20px}.profile-form[_ngcontent-%COMP%]{display:flex;flex-direction:column}.form-section[_ngcontent-%COMP%]{margin-bottom:24px}.form-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:500;margin-bottom:16px;color:#333}.form-row[_ngcontent-%COMP%]{display:flex;gap:16px}.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1}.full-width[_ngcontent-%COMP%]{width:100%}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:16px}.account-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;font-size:16px}mat-spinner[_ngcontent-%COMP%]{display:inline-block;margin-right:8px}@media (max-width: 768px){.profile-content[_ngcontent-%COMP%]{grid-template-columns:1fr}.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}