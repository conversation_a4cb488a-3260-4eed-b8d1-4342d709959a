{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_mat_spinner_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 12);\n  }\n}\nfunction LoginComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Login\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, router, route, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.returnUrl = '/';\n      this.hidePassword = true;\n    }\n    ngOnInit() {\n      // Initialize form\n      this.loginForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]]\n      });\n      // Get return URL from route parameters or default to '/'\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';\n      // Redirect if already logged in\n      if (this.authService.checkAuth()) {\n        this.router.navigate([this.returnUrl]);\n      }\n    }\n    // Convenience getter for easy access to form fields\n    get f() {\n      return this.loginForm.controls;\n    }\n    onSubmit() {\n      // Stop here if form is invalid\n      if (this.loginForm.invalid) {\n        return;\n      }\n      this.loading = true;\n      const loginRequest = {\n        email: this.f['email'].value,\n        password: this.f['password'].value\n      };\n      this.authService.login(loginRequest).subscribe({\n        next: () => {\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Login failed', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 31,\n        vars: 10,\n        consts: [[1, \"login-container\"], [1, \"login-card\"], [3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"required\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"required\", \"\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"button-container\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"routerLink\", \"/register\"], [\"diameter\", \"20\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"Login to FindIt\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"form\", 2);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_6_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(7, \"mat-form-field\", 3)(8, \"mat-label\");\n            i0.ɵɵtext(9, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 4);\n            i0.ɵɵtemplate(11, LoginComponent_mat_error_11_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵtemplate(12, LoginComponent_mat_error_12_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-form-field\", 3)(14, \"mat-label\");\n            i0.ɵɵtext(15, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"input\", 6);\n            i0.ɵɵelementStart(17, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_17_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(18, \"mat-icon\");\n            i0.ɵɵtext(19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(20, LoginComponent_mat_error_20_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵtemplate(21, LoginComponent_mat_error_21_Template, 2, 0, \"mat-error\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 8)(23, \"button\", 9);\n            i0.ɵɵtemplate(24, LoginComponent_mat_spinner_24_Template, 1, 0, \"mat-spinner\", 10);\n            i0.ɵɵtemplate(25, LoginComponent_span_25_Template, 2, 0, \"span\", 5);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(26, \"mat-card-actions\")(27, \"p\");\n            i0.ɵɵtext(28, \"Don't have an account? \");\n            i0.ɵɵelementStart(29, \"a\", 11);\n            i0.ɵɵtext(30, \"Register\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"email\"].errors == null ? null : ctx.f[\"email\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"email\"].errors == null ? null : ctx.f[\"email\"].errors[\"email\"]);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"minlength\"]);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i5.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardActions, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatIcon, i11.MatProgressSpinner],\n        styles: [\".login-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:calc(100vh - 64px);padding:20px}.login-card[_ngcontent-%COMP%]{max-width:400px;width:100%}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:15px}.button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:20px;margin-bottom:10px}mat-card-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:0 16px 16px}mat-spinner[_ngcontent-%COMP%]{display:inline-block;margin-right:5px}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}