{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/toolbar\";\nimport * as i5 from \"@angular/material/button\";\nfunction HeaderComponent_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 9);\n    i0.ɵɵtext(2, \"My Jobs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 10);\n    i0.ɵɵtext(4, \"Post Job\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HeaderComponent_ng_container_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 11);\n    i0.ɵɵtext(2, \"My Applications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HeaderComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, HeaderComponent_ng_container_6_ng_container_1_Template, 5, 0, \"ng-container\", 6);\n    i0.ɵɵtemplate(2, HeaderComponent_ng_container_6_ng_container_2_Template, 3, 0, \"ng-container\", 6);\n    i0.ɵɵelementStart(3, \"a\", 7);\n    i0.ɵɵtext(4, \"Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵtext(6, \"Logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEmployer());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isJobSeeker());\n  }\n}\nfunction HeaderComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵtext(1, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 13);\n    i0.ɵɵtext(3, \"Register\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let HeaderComponent = /*#__PURE__*/(() => {\n  class HeaderComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    ngOnInit() {\n      this.isAuthenticated$ = this.authService.isAuthenticated;\n      this.currentUser$ = this.authService.currentUser;\n    }\n    logout() {\n      this.authService.logout();\n    }\n    isEmployer() {\n      return this.authService.isEmployer();\n    }\n    isJobSeeker() {\n      return this.authService.isJobSeeker();\n    }\n    static {\n      this.ɵfac = function HeaderComponent_Factory(t) {\n        return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HeaderComponent,\n        selectors: [[\"app-header\"]],\n        decls: 10,\n        vars: 4,\n        consts: [[\"color\", \"primary\"], [\"mat-button\", \"\", \"routerLink\", \"/\", 1, \"logo\"], [1, \"spacer\"], [\"mat-button\", \"\", \"routerLink\", \"/jobs\", \"routerLinkActive\", \"active\"], [4, \"ngIf\", \"ngIfElse\"], [\"unauthenticatedLinks\", \"\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"routerLink\", \"/profile\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-button\", \"\", \"routerLink\", \"/my-jobs\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/create-job\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/my-applications\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/login\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/register\", \"routerLinkActive\", \"active\"]],\n        template: function HeaderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-toolbar\", 0)(1, \"a\", 1);\n            i0.ɵɵtext(2, \"FindIt\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(3, \"span\", 2);\n            i0.ɵɵelementStart(4, \"a\", 3);\n            i0.ɵɵtext(5, \"Browse Jobs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(6, HeaderComponent_ng_container_6_Template, 7, 2, \"ng-container\", 4);\n            i0.ɵɵpipe(7, \"async\");\n            i0.ɵɵtemplate(8, HeaderComponent_ng_template_8_Template, 4, 0, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const _r1 = i0.ɵɵreference(9);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(7, 2, ctx.isAuthenticated$))(\"ngIfElse\", _r1);\n          }\n        },\n        dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatToolbar, i5.MatAnchor, i5.MatButton, i3.AsyncPipe],\n        styles: [\".spacer[_ngcontent-%COMP%]{flex:1 1 auto}.logo[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;text-decoration:none;color:#fff}.active[_ngcontent-%COMP%]{background-color:#ffffff1a}\"]\n      });\n    }\n  }\n  return HeaderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}