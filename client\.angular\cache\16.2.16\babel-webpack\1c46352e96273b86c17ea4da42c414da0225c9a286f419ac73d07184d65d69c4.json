{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/job.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"../../services/application.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/chips\";\nfunction JobDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"mat-spinner\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"attach_money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.job.salaryRange, \"\");\n  }\n}\nfunction JobDetailComponent_div_7_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h3\");\n    i0.ɵɵtext(2, \"Requirements\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.job.requirements);\n  }\n}\nfunction JobDetailComponent_div_7_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h3\");\n    i0.ɵɵtext(2, \"Your Application Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"mat-chip\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.getApplicationStatusClass(ctx_r5.userApplication.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 3, ctx_r5.userApplication.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Applied on: \", i0.ɵɵpipeBind1(9, 5, ctx_r5.userApplication.createdAt), \"\");\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_1_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 26);\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Apply Now\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function JobDetailComponent_div_7_div_37_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.applyForJob());\n    });\n    i0.ɵɵtemplate(1, JobDetailComponent_div_7_div_37_button_1_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 25);\n    i0.ɵɵtemplate(2, JobDetailComponent_div_7_div_37_button_1_span_2_Template, 2, 0, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.applying);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.applying);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.applying);\n  }\n}\nfunction JobDetailComponent_div_7_div_37_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵtext(1, \" Already Applied \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, JobDetailComponent_div_7_div_37_button_1_Template, 3, 3, \"button\", 22);\n    i0.ɵɵtemplate(2, JobDetailComponent_div_7_div_37_button_2_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.userApplication);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.userApplication);\n  }\n}\nfunction JobDetailComponent_div_7_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function JobDetailComponent_div_7_div_38_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.editJob());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Edit Job \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 29)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.job.status === \"active\" ? \"Close Job\" : \"Reopen Job\", \" \");\n  }\n}\nconst _c0 = function () {\n  return [\"/login\"];\n};\nconst _c1 = function (a0) {\n  return {\n    returnUrl: a0\n  };\n};\nfunction JobDetailComponent_div_7_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 30);\n    i0.ɵɵtext(2, \" Login to Apply \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c0))(\"queryParams\", i0.ɵɵpureFunction1(3, _c1, \"/jobs/\" + ctx_r8.job.id));\n  }\n}\nfunction JobDetailComponent_div_7_mat_card_40_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.job.employer == null ? null : ctx_r18.job.employer.profile == null ? null : ctx_r18.job.employer.profile.companyDescription, \" \");\n  }\n}\nfunction JobDetailComponent_div_7_mat_card_40_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" No company description available. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobDetailComponent_div_7_mat_card_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 31)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"About the Company\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, JobDetailComponent_div_7_mat_card_40_p_7_Template, 2, 1, \"p\", 11);\n    i0.ɵɵtemplate(8, JobDetailComponent_div_7_mat_card_40_p_8_Template, 2, 0, \"p\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((ctx_r9.job.employer == null ? null : ctx_r9.job.employer.profile == null ? null : ctx_r9.job.employer.profile.companyName) || \"Company\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.job.employer == null ? null : ctx_r9.job.employer.profile == null ? null : ctx_r9.job.employer.profile.companyDescription);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r9.job.employer == null ? null : ctx_r9.job.employer.profile == null ? null : ctx_r9.job.employer.profile.companyDescription));\n  }\n}\nfunction JobDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"mat-card\", 9)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\")(6, \"div\", 10)(7, \"span\")(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\")(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\")(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"work\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, JobDetailComponent_div_7_span_20_Template, 4, 1, \"span\", 11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"mat-card-content\")(22, \"div\", 12)(23, \"mat-chip\", 13);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 14);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"h3\");\n    i0.ɵɵtext(31, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, JobDetailComponent_div_7_div_34_Template, 5, 1, \"div\", 16);\n    i0.ɵɵtemplate(35, JobDetailComponent_div_7_div_35_Template, 10, 7, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"mat-card-actions\");\n    i0.ɵɵtemplate(37, JobDetailComponent_div_7_div_37_Template, 3, 2, \"div\", 11);\n    i0.ɵɵtemplate(38, JobDetailComponent_div_7_div_38_Template, 9, 1, \"div\", 11);\n    i0.ɵɵtemplate(39, JobDetailComponent_div_7_div_39_Template, 3, 5, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, JobDetailComponent_div_7_mat_card_40_Template, 9, 3, \"mat-card\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.job.title);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.job.employer == null ? null : ctx_r1.job.employer.profile == null ? null : ctx_r1.job.employer.profile.companyName) || \"Company\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.job.location, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 15, ctx_r1.job.jobType), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.job.salaryRange);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ctx_r1.job.status === \"active\" ? \"primary\" : \"warn\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 17, ctx_r1.job.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Posted: \", i0.ɵɵpipeBind1(28, 19, ctx_r1.job.createdAt), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.job.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.job.requirements);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAuthenticated() && ctx_r1.isJobSeeker() && ctx_r1.userApplication);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isJobSeeker() && ctx_r1.job.status === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEmployer() && ctx_r1.isJobOwner());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isAuthenticated() && ctx_r1.job.status === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.job.employer == null ? null : ctx_r1.job.employer.profile);\n  }\n}\nfunction JobDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Job not found or has been removed.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function JobDetailComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.goBack());\n    });\n    i0.ɵɵtext(6, \" Back to Jobs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let JobDetailComponent = /*#__PURE__*/(() => {\n  class JobDetailComponent {\n    constructor(route, router, jobService, authService, applicationService, snackBar) {\n      this.route = route;\n      this.router = router;\n      this.jobService = jobService;\n      this.authService = authService;\n      this.applicationService = applicationService;\n      this.snackBar = snackBar;\n      this.job = null;\n      this.loading = true;\n      this.applying = false;\n      this.userApplication = null;\n    }\n    ngOnInit() {\n      this.loadJob();\n    }\n    loadJob() {\n      const jobId = Number(this.route.snapshot.paramMap.get('id'));\n      if (!jobId) {\n        this.router.navigate(['/jobs']);\n        return;\n      }\n      this.loading = true;\n      this.jobService.getJobById(jobId).subscribe({\n        next: response => {\n          this.job = response.data.job;\n          this.loading = false;\n          // If user is authenticated and is a job seeker, check if they've already applied\n          if (this.isAuthenticated() && this.isJobSeeker()) {\n            this.checkApplicationStatus(jobId);\n          }\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error loading job details', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n          this.router.navigate(['/jobs']);\n        }\n      });\n    }\n    checkApplicationStatus(jobId) {\n      this.applicationService.getUserApplications().subscribe({\n        next: response => {\n          const applications = response.data.applications;\n          this.userApplication = applications.find(app => app.jobId === jobId) || null;\n        },\n        error: error => {\n          console.error('Error checking application status:', error);\n        }\n      });\n    }\n    applyForJob() {\n      if (!this.job || !this.job.id) return;\n      if (!this.isAuthenticated()) {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: `/jobs/${this.job.id}`\n          }\n        });\n        return;\n      }\n      if (!this.isJobSeeker()) {\n        this.snackBar.open('Only job seekers can apply for jobs', 'Close', {\n          duration: 5000\n        });\n        return;\n      }\n      // Open application dialog\n      // For simplicity, we'll just apply directly here\n      // In a real app, you might want to open a dialog to collect cover letter\n      this.applying = true;\n      const applicationRequest = {\n        jobId: this.job.id,\n        coverLetter: 'I am interested in this position and would like to apply.'\n      };\n      this.applicationService.applyForJob(applicationRequest).subscribe({\n        next: response => {\n          this.snackBar.open('Application submitted successfully!', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          this.userApplication = response.data.application;\n          this.applying = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error submitting application', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.applying = false;\n        }\n      });\n    }\n    isAuthenticated() {\n      return this.authService.checkAuth();\n    }\n    isEmployer() {\n      return this.authService.isEmployer();\n    }\n    isJobSeeker() {\n      return this.authService.isJobSeeker();\n    }\n    isJobOwner() {\n      if (!this.job || !this.isAuthenticated()) return false;\n      // Use currentUserValue instead of getCurrentUser()\n      const currentUser = this.authService.currentUserValue;\n      return currentUser?.id === this.job.employerId;\n    }\n    getApplicationStatusClass(status) {\n      switch (status) {\n        case 'applied':\n          return 'status-applied';\n        case 'reviewed':\n          return 'status-reviewed';\n        case 'interviewed':\n          return 'status-interviewed';\n        case 'rejected':\n          return 'status-rejected';\n        case 'hired':\n          return 'status-hired';\n        default:\n          return '';\n      }\n    }\n    editJob() {\n      if (!this.job || !this.job.id) return;\n      this.router.navigate(['/create-job'], {\n        queryParams: {\n          id: this.job.id\n        }\n      });\n    }\n    goBack() {\n      this.router.navigate(['/jobs']);\n    }\n    static {\n      this.ɵfac = function JobDetailComponent_Factory(t) {\n        return new (t || JobDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.JobService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ApplicationService), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: JobDetailComponent,\n        selectors: [[\"app-job-detail\"]],\n        decls: 9,\n        vars: 3,\n        consts: [[1, \"job-detail-container\"], [1, \"back-button\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"job-content\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"job-content\"], [1, \"job-card\"], [1, \"job-subtitle\"], [4, \"ngIf\"], [1, \"job-status\"], [\"selected\", \"\", 3, \"color\"], [1, \"job-date\"], [1, \"job-section\"], [\"class\", \"job-section\", 4, \"ngIf\"], [\"class\", \"application-status\", 4, \"ngIf\"], [\"class\", \"company-card\", 4, \"ngIf\"], [1, \"application-status\"], [1, \"status-chip\"], [\"selected\", \"\", 3, \"ngClass\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"disabled\", \"\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"disabled\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 1, \"action-button\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"routerLink\", \"queryParams\"], [1, \"company-card\"], [1, \"error-container\"]],\n        template: function JobDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function JobDetailComponent_Template_button_click_2_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵelementStart(3, \"mat-icon\");\n            i0.ɵɵtext(4, \"arrow_back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(5, \" Back to Jobs \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, JobDetailComponent_div_6_Template, 2, 0, \"div\", 3);\n            i0.ɵɵtemplate(7, JobDetailComponent_div_7_Template, 41, 21, \"div\", 4);\n            i0.ɵɵtemplate(8, JobDetailComponent_div_8_Template, 7, 0, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.job);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.job);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgIf, i1.RouterLink, i7.MatButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatProgressSpinner, i11.MatChip, i6.TitleCasePipe, i6.DatePipe],\n        styles: [\".job-detail-container[_ngcontent-%COMP%]{margin-bottom:30px}.back-button[_ngcontent-%COMP%]{margin-bottom:20px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:40px 0}.job-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr;gap:20px}.job-card[_ngcontent-%COMP%], .company-card[_ngcontent-%COMP%]{margin-bottom:20px}.job-subtitle[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-top:8px}.job-subtitle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center}.job-subtitle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;width:16px;margin-right:4px}.job-status[_ngcontent-%COMP%]{display:flex;align-items:center;margin:16px 0}.job-status[_ngcontent-%COMP%]   .job-date[_ngcontent-%COMP%]{margin-left:16px;font-size:14px;color:#666}.job-section[_ngcontent-%COMP%]{margin:24px 0}.job-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:12px;font-weight:500;color:#333}.job-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{line-height:1.6;white-space:pre-line}.application-status[_ngcontent-%COMP%]{margin-top:24px;padding:16px;background-color:#f5f5f5;border-radius:4px}.application-status[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:12px;font-weight:500}.application-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{display:flex;align-items:center}.application-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{margin-left:16px;font-size:14px;color:#666}mat-card-actions[_ngcontent-%COMP%]{padding:16px}mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:8px}.action-button[_ngcontent-%COMP%]{margin-left:8px}.error-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px 0}.error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;margin-bottom:16px;color:#f44336}.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin-bottom:24px}.status-applied[_ngcontent-%COMP%]{background-color:#2196f3!important;color:#fff!important}.status-reviewed[_ngcontent-%COMP%]{background-color:#ff9800!important;color:#fff!important}.status-interviewed[_ngcontent-%COMP%]{background-color:#9c27b0!important;color:#fff!important}.status-rejected[_ngcontent-%COMP%]{background-color:#f44336!important;color:#fff!important}.status-hired[_ngcontent-%COMP%]{background-color:#4caf50!important;color:#fff!important}@media (max-width: 768px){.job-content[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return JobDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}