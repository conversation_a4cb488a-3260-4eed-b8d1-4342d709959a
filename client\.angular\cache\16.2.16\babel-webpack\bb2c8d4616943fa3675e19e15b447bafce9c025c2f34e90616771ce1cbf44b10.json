{"ast": null, "code": "import * as i8 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i7 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Self, Attribute, Input, ViewChild, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nfunction MatSelect_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.placeholder);\n  }\n}\nfunction MatSelect_span_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.triggerValue);\n  }\n}\nfunction MatSelect_span_5_ng_content_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 0, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction MatSelect_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtemplate(1, MatSelect_span_5_span_1_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(2, MatSelect_span_5_ng_content_2_Template, 1, 0, \"ng-content\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", !!ctx_r3.customTrigger);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", true);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 15, 16);\n    i0.ɵɵlistener(\"@transformPanel.done\", function MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8._panelDoneAnimatingStream.next($event.toState));\n    })(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r4._getPanelTheme(), \"\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.panelClass)(\"@transformPanel\", \"showing\");\n    i0.ɵɵattribute(\"id\", ctx_r4.id + \"-panel\")(\"aria-multiselectable\", ctx_r4.multiple)(\"aria-label\", ctx_r4.ariaLabel || null)(\"aria-labelledby\", ctx_r4._getPanelAriaLabelledby());\n  }\n}\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nconst matSelectAnimations = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: /*#__PURE__*/trigger('transformPanelWrap', [/*#__PURE__*/transition('* => void', /*#__PURE__*/query('@transformPanel', [/*#__PURE__*/animateChild()], {\n    optional: true\n  }))]),\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: /*#__PURE__*/trigger('transformPanel', [/*#__PURE__*/state('void', /*#__PURE__*/style({\n    opacity: 0,\n    transform: 'scale(1, 0.8)'\n  })), /*#__PURE__*/transition('void => showing', /*#__PURE__*/animate('120ms cubic-bezier(0, 0, 0.2, 1)', /*#__PURE__*/style({\n    opacity: 1,\n    transform: 'scale(1, 1)'\n  }))), /*#__PURE__*/transition('* => void', /*#__PURE__*/animate('100ms linear', /*#__PURE__*/style({\n    opacity: 0\n  })))])\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = /*#__PURE__*/new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = /*#__PURE__*/new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  constructor( /** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n// Boilerplate for applying mixins to MatSelect.\n/** @docs-private */\nconst _MatSelectMixinBase = /*#__PURE__*/mixinDisableRipple( /*#__PURE__*/mixinTabIndex( /*#__PURE__*/mixinDisabled( /*#__PURE__*/mixinErrorState(class {\n  constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /**\n   * Form control bound to the component.\n   * Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  ngControl) {\n    this._elementRef = _elementRef;\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n  }\n}))));\n/** Base class with all of the `MatSelect` functionality. */\nlet _MatSelectBase = /*#__PURE__*/(() => {\n  class _MatSelectBase extends _MatSelectMixinBase {\n    /** Whether the select is focused. */\n    get focused() {\n      return this._focused || this._panelOpen;\n    }\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n      return this._placeholder;\n    }\n    set placeholder(value) {\n      this._placeholder = value;\n      this.stateChanges.next();\n    }\n    /** Whether the component is required. */\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n      this.stateChanges.next();\n    }\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n      return this._multiple;\n    }\n    set multiple(value) {\n      if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectDynamicMultipleError();\n      }\n      this._multiple = coerceBooleanProperty(value);\n    }\n    /** Whether to center the active option over the trigger. */\n    get disableOptionCentering() {\n      return this._disableOptionCentering;\n    }\n    set disableOptionCentering(value) {\n      this._disableOptionCentering = coerceBooleanProperty(value);\n    }\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n      return this._compareWith;\n    }\n    set compareWith(fn) {\n      if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonFunctionValueError();\n      }\n      this._compareWith = fn;\n      if (this._selectionModel) {\n        // A different comparator means the selection could change.\n        this._initializeSelection();\n      }\n    }\n    /** Value of the select control. */\n    get value() {\n      return this._value;\n    }\n    set value(newValue) {\n      const hasAssigned = this._assignValue(newValue);\n      if (hasAssigned) {\n        this._onChange(newValue);\n      }\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    get typeaheadDebounceInterval() {\n      return this._typeaheadDebounceInterval;\n    }\n    set typeaheadDebounceInterval(value) {\n      this._typeaheadDebounceInterval = coerceNumberProperty(value);\n    }\n    /** Unique id of the element. */\n    get id() {\n      return this._id;\n    }\n    set id(value) {\n      this._id = value || this._uid;\n      this.stateChanges.next();\n    }\n    constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n      super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n      this._viewportRuler = _viewportRuler;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._ngZone = _ngZone;\n      this._dir = _dir;\n      this._parentFormField = _parentFormField;\n      this._liveAnnouncer = _liveAnnouncer;\n      this._defaultOptions = _defaultOptions;\n      /** Whether or not the overlay panel is open. */\n      this._panelOpen = false;\n      /** Comparison function to specify which option is displayed. Defaults to object equality. */\n      this._compareWith = (o1, o2) => o1 === o2;\n      /** Unique id for this input. */\n      this._uid = `mat-select-${nextUniqueId++}`;\n      /** Current `aria-labelledby` value for the select trigger. */\n      this._triggerAriaLabelledBy = null;\n      /** Emits whenever the component is destroyed. */\n      this._destroy = new Subject();\n      /** `View -> model callback called when value changes` */\n      this._onChange = () => {};\n      /** `View -> model callback called when select has been touched` */\n      this._onTouched = () => {};\n      /** ID for the DOM node containing the select's value. */\n      this._valueId = `mat-select-value-${nextUniqueId++}`;\n      /** Emits when the panel element is finished transforming in. */\n      this._panelDoneAnimatingStream = new Subject();\n      this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n      this._focused = false;\n      /** A name for this control that can be used by `mat-form-field`. */\n      this.controlType = 'mat-select';\n      this._multiple = false;\n      this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n      /** Aria label of the select. */\n      this.ariaLabel = '';\n      /** Combined stream of all of the child options' change events. */\n      this.optionSelectionChanges = defer(() => {\n        const options = this.options;\n        if (options) {\n          return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n        }\n        return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n      });\n      /** Event emitted when the select panel has been toggled. */\n      this.openedChange = new EventEmitter();\n      /** Event emitted when the select has been opened. */\n      this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n      /** Event emitted when the select has been closed. */\n      this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n      /** Event emitted when the selected value has been changed by the user. */\n      this.selectionChange = new EventEmitter();\n      /**\n       * Event that emits whenever the raw value of the select changes. This is here primarily\n       * to facilitate the two-way binding for the `value` input.\n       * @docs-private\n       */\n      this.valueChange = new EventEmitter();\n      /**\n       * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n       * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n       * panel. Track the modal we have changed so we can undo the changes on destroy.\n       */\n      this._trackedModal = null;\n      if (this.ngControl) {\n        // Note: we provide the value accessor through here, instead of\n        // the `providers` to avoid running into a circular import.\n        this.ngControl.valueAccessor = this;\n      }\n      // Note that we only want to set this when the defaults pass it in, otherwise it should\n      // stay as `undefined` so that it falls back to the default in the key manager.\n      if (_defaultOptions?.typeaheadDebounceInterval != null) {\n        this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n      }\n      this._scrollStrategyFactory = scrollStrategyFactory;\n      this._scrollStrategy = this._scrollStrategyFactory();\n      this.tabIndex = parseInt(tabIndex) || 0;\n      // Force setter to be called in case id was not specified.\n      this.id = this.id;\n    }\n    ngOnInit() {\n      this._selectionModel = new SelectionModel(this.multiple);\n      this.stateChanges.next();\n      // We need `distinctUntilChanged` here, because some browsers will\n      // fire the animation end event twice for the same animation. See:\n      // https://github.com/angular/angular/issues/24084\n      this._panelDoneAnimatingStream.pipe(distinctUntilChanged(), takeUntil(this._destroy)).subscribe(() => this._panelDoneAnimating(this.panelOpen));\n    }\n    ngAfterContentInit() {\n      this._initKeyManager();\n      this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n        event.added.forEach(option => option.select());\n        event.removed.forEach(option => option.deselect());\n      });\n      this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n        this._resetOptions();\n        this._initializeSelection();\n      });\n    }\n    ngDoCheck() {\n      const newAriaLabelledby = this._getTriggerAriaLabelledby();\n      const ngControl = this.ngControl;\n      // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n      // is computed as a result of a content query which can cause this binding to trigger a\n      // \"changed after checked\" error.\n      if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n        const element = this._elementRef.nativeElement;\n        this._triggerAriaLabelledBy = newAriaLabelledby;\n        if (newAriaLabelledby) {\n          element.setAttribute('aria-labelledby', newAriaLabelledby);\n        } else {\n          element.removeAttribute('aria-labelledby');\n        }\n      }\n      if (ngControl) {\n        // The disabled state might go out of sync if the form group is swapped out. See #17860.\n        if (this._previousControl !== ngControl.control) {\n          if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n            this.disabled = ngControl.disabled;\n          }\n          this._previousControl = ngControl.control;\n        }\n        this.updateErrorState();\n      }\n    }\n    ngOnChanges(changes) {\n      // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n      // the parent form field know to run change detection when the disabled state changes.\n      if (changes['disabled'] || changes['userAriaDescribedBy']) {\n        this.stateChanges.next();\n      }\n      if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n        this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n      }\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._destroy.next();\n      this._destroy.complete();\n      this.stateChanges.complete();\n      this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n      this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n      if (this._canOpen()) {\n        this._applyModalPanelOwnership();\n        this._panelOpen = true;\n        this._keyManager.withHorizontalOrientation(null);\n        this._highlightCorrectOption();\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n      // the `LiveAnnouncer` and any other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      if (!modal) {\n        // Most commonly, the autocomplete trigger is not inside a modal.\n        return;\n      }\n      const panelId = `${this.id}-panel`;\n      if (this._trackedModal) {\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n      addAriaReferencedId(modal, 'aria-owns', panelId);\n      this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n      if (!this._trackedModal) {\n        // Most commonly, the autocomplete trigger is not used inside a modal.\n        return;\n      }\n      const panelId = `${this.id}-panel`;\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n      if (this._panelOpen) {\n        this._panelOpen = false;\n        this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n        this._changeDetectorRef.markForCheck();\n        this._onTouched();\n      }\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n      this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n      return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n      return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n      if (this.empty) {\n        return '';\n      }\n      if (this._multiple) {\n        const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n        if (this._isRtl()) {\n          selectedOptions.reverse();\n        }\n        // TODO(crisbeto): delimiter should be configurable for proper localization.\n        return selectedOptions.join(', ');\n      }\n      return this._selectionModel.selected[0].viewValue;\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n      return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n      if (!this.disabled) {\n        this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n      }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n      const keyCode = event.keyCode;\n      const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n      const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n      const manager = this._keyManager;\n      // Open the select on ALT + arrow key to match the native <select>\n      if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n        event.preventDefault(); // prevents the page from scrolling down when pressing space\n        this.open();\n      } else if (!this.multiple) {\n        const previouslySelectedOption = this.selected;\n        manager.onKeydown(event);\n        const selectedOption = this.selected;\n        // Since the value has changed, we need to announce it ourselves.\n        if (selectedOption && previouslySelectedOption !== selectedOption) {\n          // We set a duration on the live announcement, because we want the live element to be\n          // cleared after a while so that users can't navigate to it using the arrow keys.\n          this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n        }\n      }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n      const manager = this._keyManager;\n      const keyCode = event.keyCode;\n      const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n      const isTyping = manager.isTyping();\n      if (isArrowKey && event.altKey) {\n        // Close the select on ALT + arrow key to match the native <select>\n        event.preventDefault();\n        this.close();\n        // Don't do anything in this case if the user is typing,\n        // because the typing sequence can include the space key.\n      } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n        event.preventDefault();\n        manager.activeItem._selectViaInteraction();\n      } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n        event.preventDefault();\n        const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n        this.options.forEach(option => {\n          if (!option.disabled) {\n            hasDeselectedOptions ? option.select() : option.deselect();\n          }\n        });\n      } else {\n        const previouslyFocusedIndex = manager.activeItemIndex;\n        manager.onKeydown(event);\n        if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n          manager.activeItem._selectViaInteraction();\n        }\n      }\n    }\n    _onFocus() {\n      if (!this.disabled) {\n        this._focused = true;\n        this.stateChanges.next();\n      }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n      this._focused = false;\n      this._keyManager?.cancelTypeahead();\n      if (!this.disabled && !this.panelOpen) {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n      }\n    }\n    /**\n     * Callback that is invoked when the overlay panel has been attached.\n     */\n    _onAttached() {\n      this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n        this._changeDetectorRef.detectChanges();\n        this._positioningSettled();\n      });\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n      return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n      return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n      // Defer setting the value in order to avoid the \"Expression\n      // has changed after it was checked\" errors from Angular.\n      Promise.resolve().then(() => {\n        if (this.ngControl) {\n          this._value = this.ngControl.value;\n        }\n        this._setSelectionByValue(this._value);\n        this.stateChanges.next();\n      });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n      this.options.forEach(option => option.setInactiveStyles());\n      this._selectionModel.clear();\n      if (this.multiple && value) {\n        if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw getMatSelectNonArrayValueError();\n        }\n        value.forEach(currentValue => this._selectOptionByValue(currentValue));\n        this._sortValues();\n      } else {\n        const correspondingOption = this._selectOptionByValue(value);\n        // Shift focus to the active item. Note that we shouldn't do this in multiple\n        // mode, because we don't know what option the user interacted with last.\n        if (correspondingOption) {\n          this._keyManager.updateActiveItem(correspondingOption);\n        } else if (!this.panelOpen) {\n          // Otherwise reset the highlighted option. Note that we only want to do this while\n          // closed, because doing it while open can shift the user's focus unnecessarily.\n          this._keyManager.updateActiveItem(-1);\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n      const correspondingOption = this.options.find(option => {\n        // Skip options that are already in the model. This allows us to handle cases\n        // where the same primitive value is selected multiple times.\n        if (this._selectionModel.isSelected(option)) {\n          return false;\n        }\n        try {\n          // Treat null as a special reset value.\n          return option.value != null && this._compareWith(option.value, value);\n        } catch (error) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Notify developers of errors in their comparator.\n            console.warn(error);\n          }\n          return false;\n        }\n      });\n      if (correspondingOption) {\n        this._selectionModel.select(correspondingOption);\n      }\n      return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n      // Always re-assign an array, because it might have been mutated.\n      if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n        if (this.options) {\n          this._setSelectionByValue(newValue);\n        }\n        this._value = newValue;\n        return true;\n      }\n      return false;\n    }\n    _skipPredicate(item) {\n      return item.disabled;\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n      this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this._typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n      this._keyManager.tabOut.subscribe(() => {\n        if (this.panelOpen) {\n          // Select the active item when tabbing away. This is consistent with how the native\n          // select behaves. Note that we only want to do this in single selection mode.\n          if (!this.multiple && this._keyManager.activeItem) {\n            this._keyManager.activeItem._selectViaInteraction();\n          }\n          // Restore focus to the trigger before closing. Ensures that the focus\n          // position won't be lost if the user got focus into the overlay.\n          this.focus();\n          this.close();\n        }\n      });\n      this._keyManager.change.subscribe(() => {\n        if (this._panelOpen && this.panel) {\n          this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n        } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n      });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n      const changedOrDestroyed = merge(this.options.changes, this._destroy);\n      this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n        this._onSelect(event.source, event.isUserInput);\n        if (event.isUserInput && !this.multiple && this._panelOpen) {\n          this.close();\n          this.focus();\n        }\n      });\n      // Listen to changes in the internal state of the options and react accordingly.\n      // Handles cases like the labels of the selected options changing.\n      merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n        // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n        // be the result of an expression changing. We have to use `detectChanges` in order\n        // to avoid \"changed after checked\" errors (see #14793).\n        this._changeDetectorRef.detectChanges();\n        this.stateChanges.next();\n      });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n      const wasSelected = this._selectionModel.isSelected(option);\n      if (option.value == null && !this._multiple) {\n        option.deselect();\n        this._selectionModel.clear();\n        if (this.value != null) {\n          this._propagateChanges(option.value);\n        }\n      } else {\n        if (wasSelected !== option.selected) {\n          option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n        }\n        if (isUserInput) {\n          this._keyManager.setActiveItem(option);\n        }\n        if (this.multiple) {\n          this._sortValues();\n          if (isUserInput) {\n            // In case the user selected the option with their mouse, we\n            // want to restore focus back to the trigger, in order to\n            // prevent the select keyboard controls from clashing with\n            // the ones from `mat-option`.\n            this.focus();\n          }\n        }\n      }\n      if (wasSelected !== this._selectionModel.isSelected(option)) {\n        this._propagateChanges();\n      }\n      this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n      if (this.multiple) {\n        const options = this.options.toArray();\n        this._selectionModel.sort((a, b) => {\n          return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n        });\n        this.stateChanges.next();\n      }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n      let valueToEmit = null;\n      if (this.multiple) {\n        valueToEmit = this.selected.map(option => option.value);\n      } else {\n        valueToEmit = this.selected ? this.selected.value : fallbackValue;\n      }\n      this._value = valueToEmit;\n      this.valueChange.emit(valueToEmit);\n      this._onChange(valueToEmit);\n      this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n      if (this._keyManager) {\n        if (this.empty) {\n          // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n          // because it activates the first option that passes the skip predicate, rather than the\n          // first *enabled* option.\n          let firstEnabledOptionIndex = -1;\n          for (let index = 0; index < this.options.length; index++) {\n            const option = this.options.get(index);\n            if (!option.disabled) {\n              firstEnabledOptionIndex = index;\n              break;\n            }\n          }\n          this._keyManager.setActiveItem(firstEnabledOptionIndex);\n        } else {\n          this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n        }\n      }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n      return !this._panelOpen && !this.disabled && this.options?.length > 0;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n      this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n      if (this.ariaLabel) {\n        return null;\n      }\n      const labelId = this._parentFormField?.getLabelId();\n      const labelExpression = labelId ? labelId + ' ' : '';\n      return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n      if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n        return this._keyManager.activeItem.id;\n      }\n      return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n      if (this.ariaLabel) {\n        return null;\n      }\n      const labelId = this._parentFormField?.getLabelId();\n      let value = (labelId ? labelId + ' ' : '') + this._valueId;\n      if (this.ariaLabelledby) {\n        value += ' ' + this.ariaLabelledby;\n      }\n      return value;\n    }\n    /** Called when the overlay panel is done animating. */\n    _panelDoneAnimating(isOpen) {\n      this.openedChange.emit(isOpen);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n      if (ids.length) {\n        this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        this._elementRef.nativeElement.removeAttribute('aria-describedby');\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n      this.focus();\n      this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n      return this._panelOpen || !this.empty || this._focused && !!this._placeholder;\n    }\n    static {\n      this.ɵfac = function _MatSelectBase_Factory(t) {\n        return new (t || _MatSelectBase)(i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ErrorStateMatcher), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NgForm, 8), i0.ɵɵdirectiveInject(i4.FormGroupDirective, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i4.NgControl, 10), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SELECT_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.LiveAnnouncer), i0.ɵɵdirectiveInject(MAT_SELECT_CONFIG, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: _MatSelectBase,\n        viewQuery: function _MatSelectBase_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n          }\n        },\n        inputs: {\n          userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"],\n          panelClass: \"panelClass\",\n          placeholder: \"placeholder\",\n          required: \"required\",\n          multiple: \"multiple\",\n          disableOptionCentering: \"disableOptionCentering\",\n          compareWith: \"compareWith\",\n          value: \"value\",\n          ariaLabel: [\"aria-label\", \"ariaLabel\"],\n          ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n          errorStateMatcher: \"errorStateMatcher\",\n          typeaheadDebounceInterval: \"typeaheadDebounceInterval\",\n          sortComparator: \"sortComparator\",\n          id: \"id\"\n        },\n        outputs: {\n          openedChange: \"openedChange\",\n          _openedStream: \"opened\",\n          _closedStream: \"closed\",\n          selectionChange: \"selectionChange\",\n          valueChange: \"valueChange\"\n        },\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return _MatSelectBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nlet MatSelectTrigger = /*#__PURE__*/(() => {\n  class MatSelectTrigger {\n    static {\n      this.ɵfac = function MatSelectTrigger_Factory(t) {\n        return new (t || MatSelectTrigger)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatSelectTrigger,\n        selectors: [[\"mat-select-trigger\"]],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_SELECT_TRIGGER,\n          useExisting: MatSelectTrigger\n        }])]\n      });\n    }\n  }\n  return MatSelectTrigger;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSelect = /*#__PURE__*/(() => {\n  class MatSelect extends _MatSelectBase {\n    constructor() {\n      super(...arguments);\n      /**\n       * Width of the panel. If set to `auto`, the panel will match the trigger width.\n       * If set to null or an empty string, the panel will grow to match the longest option's text.\n       */\n      this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n      this._positions = [{\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top'\n      }, {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top'\n      }, {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n        panelClass: 'mat-mdc-select-panel-above'\n      }, {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n        panelClass: 'mat-mdc-select-panel-above'\n      }];\n      this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n      // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n      // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n      // recommendation.\n      //\n      // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n      // makes a few exceptions for compound widgets.\n      //\n      // From [Developing a Keyboard Interface](\n      // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n      //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n      //   Listbox...\"\n      //\n      // The user can focus disabled options using the keyboard, but the user cannot click disabled\n      // options.\n      this._skipPredicate = option => {\n        if (this.panelOpen) {\n          // Support keyboard focusing disabled options in an ARIA listbox.\n          return false;\n        }\n        // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n        // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n        // closed.\n        return option.disabled;\n      };\n    }\n    get shouldLabelFloat() {\n      // Since the panel doesn't overlap the trigger, we\n      // want the label to only float when there's a value.\n      return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n    }\n    ngOnInit() {\n      super.ngOnInit();\n      this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n        if (this.panelOpen) {\n          this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n          this._changeDetectorRef.detectChanges();\n        }\n      });\n    }\n    open() {\n      // It's important that we read this as late as possible, because doing so earlier will\n      // return a different element since it's based on queries in the form field which may\n      // not have run yet. Also this needs to be assigned before we measure the overlay width.\n      if (this._parentFormField) {\n        this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n      }\n      this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n      super.open();\n      // Required for the MDC form field to pick up when the overlay has been opened.\n      this.stateChanges.next();\n    }\n    close() {\n      super.close();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n    }\n    /** Scrolls the active option into view. */\n    _scrollOptionIntoView(index) {\n      const option = this.options.toArray()[index];\n      if (option) {\n        const panel = this.panel.nativeElement;\n        const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n        const element = option._getHostElement();\n        if (index === 0 && labelCount === 1) {\n          // If we've got one group label before the option and we're at the top option,\n          // scroll the list to the top. This is better UX than scrolling the list to the\n          // top of the option, because it allows the user to read the top group's label.\n          panel.scrollTop = 0;\n        } else {\n          panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n        }\n      }\n    }\n    _positioningSettled() {\n      this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    _getChangeEvent(value) {\n      return new MatSelectChange(this, value);\n    }\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n      if (this.panelWidth === 'auto') {\n        const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n        return refToMeasure.nativeElement.getBoundingClientRect().width;\n      }\n      return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n      this._syncParentProperties();\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n      if (this.options) {\n        for (const option of this.options) {\n          option._changeDetectorRef.markForCheck();\n        }\n      }\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */function () {\n        let ɵMatSelect_BaseFactory;\n        return function MatSelect_Factory(t) {\n          return (ɵMatSelect_BaseFactory || (ɵMatSelect_BaseFactory = i0.ɵɵgetInheritedFactory(MatSelect)))(t || MatSelect);\n        };\n      }();\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatSelect,\n        selectors: [[\"mat-select\"]],\n        contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n            i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n          }\n        },\n        hostAttrs: [\"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"listbox\", \"ngSkipHydration\", \"\", 1, \"mat-mdc-select\"],\n        hostVars: 19,\n        hostBindings: function MatSelect_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n              return ctx._handleKeydown($event);\n            })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n              return ctx._onFocus();\n            })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n              return ctx._onBlur();\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n            i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          tabIndex: \"tabIndex\",\n          panelWidth: \"panelWidth\",\n          hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\"\n        },\n        exportAs: [\"matSelect\"],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MatFormFieldControl,\n          useExisting: MatSelect\n        }, {\n          provide: MAT_OPTION_PARENT_COMPONENT,\n          useExisting: MatSelect\n        }]), i0.ɵɵInheritDefinitionFeature],\n        ngContentSelectors: _c3,\n        decls: 11,\n        vars: 10,\n        consts: [[\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [1, \"mat-mdc-select-value\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-select-placeholder mat-mdc-select-min-line\", 4, \"ngSwitchCase\"], [\"class\", \"mat-mdc-select-value-text\", 3, \"ngSwitch\", 4, \"ngSwitchCase\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"backdropClick\", \"attach\", \"detach\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-select-min-line\", 4, \"ngSwitchDefault\"], [4, \"ngSwitchCase\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"ngClass\", \"keydown\"], [\"panel\", \"\"]],\n        template: function MatSelect_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c2);\n            i0.ɵɵelementStart(0, \"div\", 0, 1);\n            i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n              return ctx.toggle();\n            });\n            i0.ɵɵelementStart(3, \"div\", 2);\n            i0.ɵɵtemplate(4, MatSelect_span_4_Template, 2, 1, \"span\", 3);\n            i0.ɵɵtemplate(5, MatSelect_span_5_Template, 3, 2, \"span\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(8, \"svg\", 7);\n            i0.ɵɵelement(9, \"path\", 8);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 9, \"ng-template\", 9);\n            i0.ɵɵlistener(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n              return ctx.close();\n            })(\"attach\", function MatSelect_Template_ng_template_attach_10_listener() {\n              return ctx._onAttached();\n            })(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n              return ctx.close();\n            });\n          }\n          if (rf & 2) {\n            const _r0 = i0.ɵɵreference(1);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngSwitch\", ctx.empty);\n            i0.ɵɵattribute(\"id\", ctx._valueId);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", true);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", false);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || _r0)(\"cdkConnectedOverlayOpen\", ctx.panelOpen)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth);\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgSwitch, i7.NgSwitchCase, i7.NgSwitchDefault, i8.CdkConnectedOverlay, i8.CdkOverlayOrigin],\n        styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matSelectAnimations.transformPanel]\n        },\n        changeDetection: 0\n      });\n    }\n  }\n  return MatSelect;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSelectModule = /*#__PURE__*/(() => {\n  class MatSelectModule {\n    static {\n      this.ɵfac = function MatSelectModule_Factory(t) {\n        return new (t || MatSelectModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatSelectModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n        imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n      });\n    }\n  }\n  return MatSelectModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };\n//# sourceMappingURL=select.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}