{"ast": null, "code": "import { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/application.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/paginator\";\nimport * as i10 from \"@angular/material/sort\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/menu\";\nfunction ApplicationListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"mat-spinner\", 6);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"folder_open\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 8);\n    i0.ɵɵtext(6, \"Browse Jobs\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.isJobSeeker() ? \"You have not applied to any jobs yet.\" : \"No applications found.\");\n  }\n}\nfunction ApplicationListComponent_div_6_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Job Title\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_4_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const application_r17 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.viewJobDetails(application_r17.jobId));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const application_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (application_r17.job == null ? null : application_r17.job.title) || \"Unknown Job\", \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Company\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const application_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (application_r20.job == null ? null : application_r20.job.employer == null ? null : application_r20.job.employer.profile == null ? null : application_r20.job.employer.profile.companyName) || \"Unknown Company\", \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Applicant\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const application_r21 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ((application_r21.applicant == null ? null : application_r21.applicant.profile == null ? null : application_r21.applicant.profile.firstName) || \"\") + \" \" + ((application_r21.applicant == null ? null : application_r21.applicant.profile == null ? null : application_r21.applicant.profile.lastName) || \"\") || (application_r21.applicant == null ? null : application_r21.applicant.email) || \"Unknown Applicant\", \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Applied Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const application_r22 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, application_r22.createdAt), \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const application_r23 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r12.getStatusClass(application_r23.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, application_r23.status), \" \");\n  }\n}\nfunction ApplicationListComponent_div_6_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationListComponent_div_6_td_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 31)(6, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.updateStatus(application_r24, \"applied\"));\n    });\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Applied\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.updateStatus(application_r24, \"reviewed\"));\n    });\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Reviewed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.updateStatus(application_r24, \"interviewed\"));\n    });\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Interviewed\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.updateStatus(application_r24, \"rejected\"));\n    });\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Rejected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.updateStatus(application_r24, \"hired\"));\n    });\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Hired\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r27 = i0.ɵɵreference(5);\n    const application_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r27);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"applied\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"reviewed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"interviewed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"rejected\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", application_r24.status === \"hired\");\n  }\n}\nfunction ApplicationListComponent_div_6_td_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ApplicationListComponent_div_6_td_19_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const application_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.viewJobDetails(application_r24.jobId));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ApplicationListComponent_div_6_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24);\n    i0.ɵɵtemplate(1, ApplicationListComponent_div_6_td_19_div_1_Template, 21, 6, \"div\", 28);\n    i0.ɵɵtemplate(2, ApplicationListComponent_div_6_td_19_div_2_Template, 4, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isEmployer());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isJobSeeker());\n  }\n}\nfunction ApplicationListComponent_div_6_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 34);\n  }\n}\nfunction ApplicationListComponent_div_6_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 35);\n  }\n}\nconst _c0 = function () {\n  return [5, 10, 25, 50];\n};\nfunction ApplicationListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"table\", 10);\n    i0.ɵɵelementContainerStart(2, 11);\n    i0.ɵɵtemplate(3, ApplicationListComponent_div_6_th_3_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(4, ApplicationListComponent_div_6_td_4_Template, 3, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 14);\n    i0.ɵɵtemplate(6, ApplicationListComponent_div_6_th_6_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(7, ApplicationListComponent_div_6_td_7_Template, 2, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 15);\n    i0.ɵɵtemplate(9, ApplicationListComponent_div_6_th_9_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(10, ApplicationListComponent_div_6_td_10_Template, 2, 1, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 16);\n    i0.ɵɵtemplate(12, ApplicationListComponent_div_6_th_12_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(13, ApplicationListComponent_div_6_td_13_Template, 3, 3, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 17);\n    i0.ɵɵtemplate(15, ApplicationListComponent_div_6_th_15_Template, 2, 0, \"th\", 12);\n    i0.ɵɵtemplate(16, ApplicationListComponent_div_6_td_16_Template, 4, 4, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 18);\n    i0.ɵɵtemplate(18, ApplicationListComponent_div_6_th_18_Template, 2, 0, \"th\", 19);\n    i0.ɵɵtemplate(19, ApplicationListComponent_div_6_td_19_Template, 3, 2, \"td\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(20, ApplicationListComponent_div_6_tr_20_Template, 1, 0, \"tr\", 20);\n    i0.ɵɵtemplate(21, ApplicationListComponent_div_6_tr_21_Template, 1, 0, \"tr\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-paginator\", 22);\n    i0.ɵɵlistener(\"page\", function ApplicationListComponent_div_6_Template_mat_paginator_page_22_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"dataSource\", ctx_r2.dataSource);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r2.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"length\", ctx_r2.totalItems)(\"pageSize\", ctx_r2.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(7, _c0))(\"pageIndex\", ctx_r2.currentPage);\n  }\n}\nexport let ApplicationListComponent = /*#__PURE__*/(() => {\n  class ApplicationListComponent {\n    constructor(applicationService, authService, route, router, snackBar, dialog) {\n      this.applicationService = applicationService;\n      this.authService = authService;\n      this.route = route;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.applications = [];\n      this.dataSource = new MatTableDataSource([]);\n      this.loading = false;\n      this.totalItems = 0;\n      this.pageSize = 10;\n      this.currentPage = 0;\n      this.displayedColumns = [];\n    }\n    ngOnInit() {\n      // Check if we're viewing applications for a specific job\n      this.route.paramMap.subscribe(params => {\n        const jobIdParam = params.get('jobId');\n        if (jobIdParam) {\n          this.jobId = +jobIdParam;\n        }\n      });\n      // Set displayed columns based on user role\n      this.setDisplayedColumns();\n      // Load applications\n      this.loadApplications();\n    }\n    setDisplayedColumns() {\n      if (this.isEmployer()) {\n        this.displayedColumns = ['applicantName', 'jobTitle', 'appliedDate', 'status', 'actions'];\n      } else {\n        this.displayedColumns = ['jobTitle', 'companyName', 'appliedDate', 'status', 'actions'];\n      }\n    }\n    loadApplications() {\n      this.loading = true;\n      const params = {\n        page: this.currentPage + 1,\n        limit: this.pageSize\n      };\n      // Determine which service method to call based on user role and context\n      let applicationsObservable;\n      if (this.isEmployer() && this.jobId) {\n        // Employer viewing applications for a specific job\n        applicationsObservable = this.applicationService.getJobApplications(this.jobId, params);\n      } else if (this.isJobSeeker()) {\n        // Job seeker viewing their own applications\n        applicationsObservable = this.applicationService.getUserApplications(params);\n      } else {\n        // Unauthorized access\n        this.snackBar.open('Unauthorized access', 'Close', {\n          duration: 5000\n        });\n        this.router.navigate(['/jobs']);\n        return;\n      }\n      applicationsObservable.subscribe({\n        next: response => {\n          this.applications = response.data.applications;\n          this.dataSource = new MatTableDataSource(this.applications);\n          this.totalItems = response.data.pagination.totalItems;\n          this.loading = false;\n          // Set up sorting and pagination after data is loaded\n          setTimeout(() => {\n            if (this.sort) {\n              this.dataSource.sort = this.sort;\n            }\n            if (this.paginator) {\n              this.dataSource.paginator = this.paginator;\n            }\n          });\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error loading applications', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n        }\n      });\n    }\n    onPageChange(event) {\n      this.currentPage = event.pageIndex;\n      this.pageSize = event.pageSize;\n      this.loadApplications();\n    }\n    updateStatus(application, newStatus) {\n      if (!application.id) return;\n      const statusRequest = {\n        status: newStatus\n      };\n      this.applicationService.updateApplicationStatus(application.id, statusRequest).subscribe({\n        next: response => {\n          this.snackBar.open('Application status updated successfully!', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Update the application in the list\n          const index = this.applications.findIndex(app => app.id === application.id);\n          if (index !== -1) {\n            this.applications[index] = response.data.application;\n            this.dataSource.data = [...this.applications];\n          }\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error updating application status', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    viewJobDetails(jobId) {\n      this.router.navigate(['/jobs', jobId]);\n    }\n    getStatusClass(status) {\n      switch (status) {\n        case 'applied':\n          return 'status-applied';\n        case 'reviewed':\n          return 'status-reviewed';\n        case 'interviewed':\n          return 'status-interviewed';\n        case 'rejected':\n          return 'status-rejected';\n        case 'hired':\n          return 'status-hired';\n        default:\n          return '';\n      }\n    }\n    isEmployer() {\n      return this.authService.isEmployer();\n    }\n    isJobSeeker() {\n      return this.authService.isJobSeeker();\n    }\n    static {\n      this.ɵfac = function ApplicationListComponent_Factory(t) {\n        return new (t || ApplicationListComponent)(i0.ɵɵdirectiveInject(i1.ApplicationService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ApplicationListComponent,\n        selectors: [[\"app-application-list\"]],\n        viewQuery: function ApplicationListComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"application-list-container\"], [1, \"application-list-header\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"applications-table-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"no-results\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/jobs\"], [1, \"applications-table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"applications-table\", 3, \"dataSource\"], [\"matColumnDef\", \"jobTitle\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"companyName\"], [\"matColumnDef\", \"applicantName\"], [\"matColumnDef\", \"appliedDate\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"aria-label\", \"Select page\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"job-link\", 3, \"click\"], [1, \"status-chip\", 3, \"ngClass\"], [\"mat-header-cell\", \"\"], [\"class\", \"action-buttons\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Change status\", 3, \"matMenuTriggerFor\"], [\"statusMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"disabled\", \"click\"], [\"mat-icon-button\", \"\", \"aria-label\", \"View job\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function ApplicationListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(4, ApplicationListComponent_div_4_Template, 2, 0, \"div\", 2);\n            i0.ɵɵtemplate(5, ApplicationListComponent_div_5_Template, 7, 1, \"div\", 3);\n            i0.ɵɵtemplate(6, ApplicationListComponent_div_6_Template, 23, 8, \"div\", 4);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.isJobSeeker() ? \"My Applications\" : ctx.jobId ? \"Applications for This Job\" : \"All Applications\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.applications.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.applications.length > 0);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgIf, i3.RouterLink, i7.MatButton, i7.MatIconButton, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, i9.MatPaginator, i10.MatSort, i10.MatSortHeader, i11.MatIcon, i12.MatProgressSpinner, i13.MatMenu, i13.MatMenuItem, i13.MatMenuTrigger, i6.TitleCasePipe, i6.DatePipe],\n        styles: [\".application-list-container[_ngcontent-%COMP%]{margin-bottom:30px}.application-list-header[_ngcontent-%COMP%]{margin-bottom:20px}.application-list-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin-bottom:8px;font-weight:500}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:40px 0}.no-results[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px 0;color:#666}.no-results[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;margin-bottom:16px}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin-bottom:24px}.applications-table-container[_ngcontent-%COMP%]{overflow-x:auto}.applications-table[_ngcontent-%COMP%]{width:100%}.applications-table[_ngcontent-%COMP%]   th.mat-header-cell[_ngcontent-%COMP%]{font-weight:500;color:#333}.applications-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.job-link[_ngcontent-%COMP%]{color:#3f51b5;cursor:pointer;text-decoration:none}.job-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.status-chip[_ngcontent-%COMP%]{padding:4px 8px;border-radius:16px;font-size:12px;font-weight:500;display:inline-block}.action-buttons[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.status-applied[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff}.status-reviewed[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.status-interviewed[_ngcontent-%COMP%]{background-color:#9c27b0;color:#fff}.status-rejected[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.status-hired[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}@media (max-width: 768px){.applications-table[_ngcontent-%COMP%]   th.mat-header-cell[_ngcontent-%COMP%], .applications-table[_ngcontent-%COMP%]   td.mat-cell[_ngcontent-%COMP%]{padding:8px}}\"]\n      });\n    }\n  }\n  return ApplicationListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}