{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      if (this.authService.checkAuth()) {\n        return true;\n      } else {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: state.url\n          }\n        });\n        return false;\n      }\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(t) {\n        return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}