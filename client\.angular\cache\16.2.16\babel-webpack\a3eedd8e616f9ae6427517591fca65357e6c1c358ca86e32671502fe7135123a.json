{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nfunction JobFormComponent_mat_error_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job title is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job title cannot exceed 100 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Location is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Location cannot exceed 100 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r13.label, \" \");\n  }\n}\nfunction JobFormComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job type is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Salary range cannot exceed 50 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job description is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Job description cannot exceed 5000 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Requirements cannot exceed 2000 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobFormComponent_mat_form_field_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 1)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 16)(4, \"option\", 17);\n    i0.ɵɵtext(5, \"Active\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 18);\n    i0.ɵɵtext(7, \"Closed\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction JobFormComponent_mat_spinner_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 19);\n  }\n}\nfunction JobFormComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate((ctx_r12.job == null ? null : ctx_r12.job.id) ? \"Update Job\" : \"Create Job\");\n  }\n}\nexport let JobFormComponent = /*#__PURE__*/(() => {\n  class JobFormComponent {\n    constructor(formBuilder) {\n      this.formBuilder = formBuilder;\n      this.job = null;\n      this.loading = false;\n      this.formSubmit = new EventEmitter();\n      this.formCancel = new EventEmitter();\n      this.jobTypes = [{\n        value: 'full-time',\n        label: 'Full Time'\n      }, {\n        value: 'part-time',\n        label: 'Part Time'\n      }, {\n        value: 'contract',\n        label: 'Contract'\n      }, {\n        value: 'internship',\n        label: 'Internship'\n      }, {\n        value: 'remote',\n        label: 'Remote'\n      }];\n    }\n    ngOnInit() {\n      this.initForm();\n      console.log('ngOnInit job:', this.job);\n    }\n    ngOnChanges(changes) {\n      // If the job input changes and the form is already initialized\n      if (changes['job'] && this.jobForm) {\n        console.log('ngOnChanges job:', this.job);\n        // Only update if the job is not null and has changed\n        if (this.job && (!changes['job'].firstChange || changes['job'].currentValue !== changes['job'].previousValue)) {\n          this.updateForm();\n        }\n      }\n    }\n    initForm() {\n      this.jobForm = this.formBuilder.group({\n        title: [this.job?.title || '', [Validators.required, Validators.maxLength(100)]],\n        description: [this.job?.description || '', [Validators.required, Validators.maxLength(5000)]],\n        location: [this.job?.location || '', [Validators.required, Validators.maxLength(100)]],\n        salaryRange: [this.job?.salaryRange || '', Validators.maxLength(50)],\n        jobType: [this.job?.jobType || 'full-time', Validators.required],\n        requirements: [this.job?.requirements || '', Validators.maxLength(2000)],\n        status: [this.job?.status || 'active']\n      });\n    }\n    updateForm() {\n      if (this.job) {\n        this.jobForm.patchValue({\n          title: this.job.title || '',\n          description: this.job.description || '',\n          location: this.job.location || '',\n          salaryRange: this.job.salaryRange || '',\n          jobType: this.job.jobType || 'full-time',\n          requirements: this.job.requirements || '',\n          status: this.job.status || 'active'\n        });\n      }\n    }\n    onSubmit() {\n      if (this.jobForm.invalid) {\n        // Mark all fields as touched to trigger validation messages\n        Object.keys(this.jobForm.controls).forEach(key => {\n          const control = this.jobForm.get(key);\n          control?.markAsTouched();\n        });\n        return;\n      }\n      const jobRequest = this.jobForm.value;\n      this.formSubmit.emit(jobRequest);\n    }\n    onCancel() {\n      this.formCancel.emit();\n    }\n    // Convenience getter for easy access to form fields\n    get f() {\n      return this.jobForm.controls;\n    }\n    static {\n      this.ɵfac = function JobFormComponent_Factory(t) {\n        return new (t || JobFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: JobFormComponent,\n        selectors: [[\"app-job-form\"]],\n        inputs: {\n          job: \"job\",\n          loading: \"loading\"\n        },\n        outputs: {\n          formSubmit: \"formSubmit\",\n          formCancel: \"formCancel\"\n        },\n        features: [i0.ɵɵNgOnChangesFeature],\n        decls: 42,\n        vars: 16,\n        consts: [[1, \"job-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"title\", \"placeholder\", \"Enter job title\", \"required\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"location\", \"placeholder\", \"Enter job location (city, state, remote)\", \"required\", \"\"], [\"matNativeControl\", \"\", \"formControlName\", \"jobType\", \"required\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"salaryRange\", \"placeholder\", \"e.g. $50,000 - $70,000\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"placeholder\", \"Enter detailed job description\", \"rows\", \"6\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"requirements\", \"placeholder\", \"Enter job requirements\", \"rows\", \"4\"], [\"appearance\", \"outline\", \"class\", \"full-width\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [3, \"value\"], [\"matNativeControl\", \"\", \"formControlName\", \"status\"], [\"value\", \"active\"], [\"value\", \"closed\"], [\"diameter\", \"20\"]],\n        template: function JobFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"form\", 0);\n            i0.ɵɵlistener(\"ngSubmit\", function JobFormComponent_Template_form_ngSubmit_0_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(1, \"mat-form-field\", 1)(2, \"mat-label\");\n            i0.ɵɵtext(3, \"Job Title\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(4, \"input\", 2);\n            i0.ɵɵtemplate(5, JobFormComponent_mat_error_5_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵtemplate(6, JobFormComponent_mat_error_6_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"mat-form-field\", 1)(8, \"mat-label\");\n            i0.ɵɵtext(9, \"Location\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 4);\n            i0.ɵɵtemplate(11, JobFormComponent_mat_error_11_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵtemplate(12, JobFormComponent_mat_error_12_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-form-field\", 1)(14, \"mat-label\");\n            i0.ɵɵtext(15, \"Job Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"select\", 5);\n            i0.ɵɵtemplate(17, JobFormComponent_option_17_Template, 2, 2, \"option\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(18, JobFormComponent_mat_error_18_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"mat-form-field\", 1)(20, \"mat-label\");\n            i0.ɵɵtext(21, \"Salary Range (Optional)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 7);\n            i0.ɵɵtemplate(23, JobFormComponent_mat_error_23_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-form-field\", 1)(25, \"mat-label\");\n            i0.ɵɵtext(26, \"Job Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(27, \"textarea\", 8);\n            i0.ɵɵtemplate(28, JobFormComponent_mat_error_28_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵtemplate(29, JobFormComponent_mat_error_29_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"mat-form-field\", 1)(31, \"mat-label\");\n            i0.ɵɵtext(32, \"Requirements (Optional)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"textarea\", 9);\n            i0.ɵɵtemplate(34, JobFormComponent_mat_error_34_Template, 2, 0, \"mat-error\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(35, JobFormComponent_mat_form_field_35_Template, 8, 0, \"mat-form-field\", 10);\n            i0.ɵɵelementStart(36, \"div\", 11)(37, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function JobFormComponent_Template_button_click_37_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(38, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"button\", 13);\n            i0.ɵɵtemplate(40, JobFormComponent_mat_spinner_40_Template, 1, 0, \"mat-spinner\", 14);\n            i0.ɵɵtemplate(41, JobFormComponent_span_41_Template, 2, 1, \"span\", 3);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"formGroup\", ctx.jobForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"title\"].errors == null ? null : ctx.f[\"title\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"title\"].errors == null ? null : ctx.f[\"title\"].errors[\"maxlength\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"location\"].errors == null ? null : ctx.f[\"location\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"location\"].errors == null ? null : ctx.f[\"location\"].errors[\"maxlength\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.jobTypes);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"jobType\"].errors == null ? null : ctx.f[\"jobType\"].errors[\"required\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"salaryRange\"].errors == null ? null : ctx.f[\"salaryRange\"].errors[\"maxlength\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"description\"].errors == null ? null : ctx.f[\"description\"].errors[\"required\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"description\"].errors == null ? null : ctx.f[\"description\"].errors[\"maxlength\"]);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.f[\"requirements\"].errors == null ? null : ctx.f[\"requirements\"].errors[\"maxlength\"]);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.job == null ? null : ctx.job.id);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i4.MatInput, i5.MatFormField, i5.MatLabel, i5.MatError, i6.MatProgressSpinner],\n        styles: [\".job-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:16px;margin-top:16px}mat-spinner[_ngcontent-%COMP%]{display:inline-block;margin-right:8px}@media (max-width: 768px){.form-actions[_ngcontent-%COMP%]{flex-direction:column-reverse}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;margin-bottom:8px}}\"]\n      });\n    }\n  }\n  return JobFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}