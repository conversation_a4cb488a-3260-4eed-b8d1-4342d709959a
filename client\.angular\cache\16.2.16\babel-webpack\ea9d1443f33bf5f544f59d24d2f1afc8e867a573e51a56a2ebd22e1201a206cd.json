{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, Attribute, Input, inject, NgZone, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, Optional, Inject, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i2 from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** The floating label for a `mat-form-field`. */\nconst _c0 = [\"notch\"];\nconst _c1 = [\"matFormFieldNotchedOutline\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = [\"textField\"];\nconst _c4 = [\"iconPrefixContainer\"];\nconst _c5 = [\"textPrefixContainer\"];\nfunction MatFormField_ng_template_0_label_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n}\nfunction MatFormField_ng_template_0_label_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 17);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, MatFormField_ng_template_0_label_0_span_2_Template, 1, 0, \"span\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"floating\", ctx_r13._shouldLabelFloat())(\"monitorResize\", ctx_r13._hasOutline())(\"id\", ctx_r13._labelId);\n    i0.ɵɵattribute(\"for\", ctx_r13._control.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.hideRequiredMarker && ctx_r13._control.required);\n  }\n}\nfunction MatFormField_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_ng_template_0_label_0_Template, 3, 5, \"label\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._hasFloatingLabel());\n  }\n}\nfunction MatFormField_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 20);\n  }\n}\nfunction MatFormField_div_6_ng_template_1_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_div_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_div_6_ng_template_1_ng_template_0_Template, 0, 0, \"ng-template\", 22);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const _r0 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r0);\n  }\n}\nfunction MatFormField_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, MatFormField_div_6_ng_template_1_Template, 1, 1, \"ng-template\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matFormFieldNotchedOutlineOpen\", ctx_r4._shouldLabelFloat());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4._forceDisplayInfixLabel());\n  }\n}\nfunction MatFormField_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23, 24);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25, 26);\n    i0.ɵɵprojection(2, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_ng_template_10_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_ng_template_10_ng_template_0_Template, 0, 0, \"ng-template\", 22);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r0 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r0);\n  }\n}\nfunction MatFormField_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵprojection(1, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵprojection(1, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 29);\n  }\n}\nfunction MatFormField_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵprojection(1, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@transitionMessages\", ctx_r11._subscriptAnimationState);\n  }\n}\nfunction MatFormField_div_17_mat_hint_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", ctx_r20._hintLabelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.hintLabel);\n  }\n}\nfunction MatFormField_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, MatFormField_div_17_mat_hint_1_Template, 2, 2, \"mat-hint\", 32);\n    i0.ɵɵprojection(2, 7);\n    i0.ɵɵelement(3, \"div\", 33);\n    i0.ɵɵprojection(4, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@transitionMessages\", ctx_r12._subscriptAnimationState);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.hintLabel);\n  }\n}\nconst _c6 = [\"*\", [[\"mat-label\"]], [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"]], [[\"\", \"matTextPrefix\", \"\"]], [[\"\", \"matTextSuffix\", \"\"]], [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"]], [[\"mat-error\"], [\"\", \"matError\", \"\"]], [[\"mat-hint\", 3, \"align\", \"end\"]], [[\"mat-hint\", \"align\", \"end\"]]];\nconst _c7 = [\"*\", \"mat-label\", \"[matPrefix], [matIconPrefix]\", \"[matTextPrefix]\", \"[matTextSuffix]\", \"[matSuffix], [matIconSuffix]\", \"mat-error, [matError]\", \"mat-hint:not([align='end'])\", \"mat-hint[align='end']\"];\nlet MatLabel = /*#__PURE__*/(() => {\n  class MatLabel {\n    static {\n      this.ɵfac = function MatLabel_Factory(t) {\n        return new (t || MatLabel)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatLabel,\n        selectors: [[\"mat-label\"]]\n      });\n    }\n  }\n  return MatLabel;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet nextUniqueId$2 = 0;\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = /*#__PURE__*/new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nlet MatError = /*#__PURE__*/(() => {\n  class MatError {\n    constructor(ariaLive, elementRef) {\n      this.id = `mat-mdc-error-${nextUniqueId$2++}`;\n      // If no aria-live value is set add 'polite' as a default. This is preferred over setting\n      // role='alert' so that screen readers do not interrupt the current task to read this aloud.\n      if (!ariaLive) {\n        elementRef.nativeElement.setAttribute('aria-live', 'polite');\n      }\n    }\n    static {\n      this.ɵfac = function MatError_Factory(t) {\n        return new (t || MatError)(i0.ɵɵinjectAttribute('aria-live'), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatError,\n        selectors: [[\"mat-error\"], [\"\", \"matError\", \"\"]],\n        hostAttrs: [\"aria-atomic\", \"true\", 1, \"mat-mdc-form-field-error\", \"mat-mdc-form-field-bottom-align\"],\n        hostVars: 1,\n        hostBindings: function MatError_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"id\", ctx.id);\n          }\n        },\n        inputs: {\n          id: \"id\"\n        },\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_ERROR,\n          useExisting: MatError\n        }])]\n      });\n    }\n  }\n  return MatError;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet nextUniqueId$1 = 0;\n/** Hint text to be shown underneath the form field control. */\nlet MatHint = /*#__PURE__*/(() => {\n  class MatHint {\n    constructor() {\n      /** Whether to align the hint label at the start or end of the line. */\n      this.align = 'start';\n      /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n      this.id = `mat-mdc-hint-${nextUniqueId$1++}`;\n    }\n    static {\n      this.ɵfac = function MatHint_Factory(t) {\n        return new (t || MatHint)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatHint,\n        selectors: [[\"mat-hint\"]],\n        hostAttrs: [1, \"mat-mdc-form-field-hint\", \"mat-mdc-form-field-bottom-align\"],\n        hostVars: 4,\n        hostBindings: function MatHint_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"id\", ctx.id);\n            i0.ɵɵattribute(\"align\", null);\n            i0.ɵɵclassProp(\"mat-mdc-form-field-hint-end\", ctx.align === \"end\");\n          }\n        },\n        inputs: {\n          align: \"align\",\n          id: \"id\"\n        }\n      });\n    }\n  }\n  return MatHint;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = /*#__PURE__*/new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nlet MatPrefix = /*#__PURE__*/(() => {\n  class MatPrefix {\n    constructor() {\n      this._isText = false;\n    }\n    set _isTextSelector(value) {\n      this._isText = true;\n    }\n    static {\n      this.ɵfac = function MatPrefix_Factory(t) {\n        return new (t || MatPrefix)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatPrefix,\n        selectors: [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"], [\"\", \"matTextPrefix\", \"\"]],\n        inputs: {\n          _isTextSelector: [\"matTextPrefix\", \"_isTextSelector\"]\n        },\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_PREFIX,\n          useExisting: MatPrefix\n        }])]\n      });\n    }\n  }\n  return MatPrefix;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = /*#__PURE__*/new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nlet MatSuffix = /*#__PURE__*/(() => {\n  class MatSuffix {\n    constructor() {\n      this._isText = false;\n    }\n    set _isTextSelector(value) {\n      this._isText = true;\n    }\n    static {\n      this.ɵfac = function MatSuffix_Factory(t) {\n        return new (t || MatSuffix)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatSuffix,\n        selectors: [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"], [\"\", \"matTextSuffix\", \"\"]],\n        inputs: {\n          _isTextSelector: [\"matTextSuffix\", \"_isTextSelector\"]\n        },\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_SUFFIX,\n          useExisting: MatSuffix\n        }])]\n      });\n    }\n  }\n  return MatSuffix;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = /*#__PURE__*/new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nlet MatFormFieldFloatingLabel = /*#__PURE__*/(() => {\n  class MatFormFieldFloatingLabel {\n    /** Whether the label is floating. */\n    get floating() {\n      return this._floating;\n    }\n    set floating(value) {\n      this._floating = value;\n      if (this.monitorResize) {\n        this._handleResize();\n      }\n    }\n    /** Whether to monitor for resize events on the floating label. */\n    get monitorResize() {\n      return this._monitorResize;\n    }\n    set monitorResize(value) {\n      this._monitorResize = value;\n      if (this._monitorResize) {\n        this._subscribeToResize();\n      } else {\n        this._resizeSubscription.unsubscribe();\n      }\n    }\n    constructor(_elementRef) {\n      this._elementRef = _elementRef;\n      this._floating = false;\n      this._monitorResize = false;\n      /** The shared ResizeObserver. */\n      this._resizeObserver = inject(SharedResizeObserver);\n      /** The Angular zone. */\n      this._ngZone = inject(NgZone);\n      /** The parent form-field. */\n      this._parent = inject(FLOATING_LABEL_PARENT);\n      /** The current resize event subscription. */\n      this._resizeSubscription = new Subscription();\n    }\n    ngOnDestroy() {\n      this._resizeSubscription.unsubscribe();\n    }\n    /** Gets the width of the label. Used for the outline notch. */\n    getWidth() {\n      return estimateScrollWidth(this._elementRef.nativeElement);\n    }\n    /** Gets the HTML element for the floating label. */\n    get element() {\n      return this._elementRef.nativeElement;\n    }\n    /** Handles resize events from the ResizeObserver. */\n    _handleResize() {\n      // In the case where the label grows in size, the following sequence of events occurs:\n      // 1. The label grows by 1px triggering the ResizeObserver\n      // 2. The notch is expanded to accommodate the entire label\n      // 3. The label expands to its full width, triggering the ResizeObserver again\n      //\n      // This is expected, but If we allow this to all happen within the same macro task it causes an\n      // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n      // the next macro task.\n      setTimeout(() => this._parent._handleLabelResized());\n    }\n    /** Subscribes to resize events. */\n    _subscribeToResize() {\n      this._resizeSubscription.unsubscribe();\n      this._ngZone.runOutsideAngular(() => {\n        this._resizeSubscription = this._resizeObserver.observe(this._elementRef.nativeElement, {\n          box: 'border-box'\n        }).subscribe(() => this._handleResize());\n      });\n    }\n    static {\n      this.ɵfac = function MatFormFieldFloatingLabel_Factory(t) {\n        return new (t || MatFormFieldFloatingLabel)(i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatFormFieldFloatingLabel,\n        selectors: [[\"label\", \"matFormFieldFloatingLabel\", \"\"]],\n        hostAttrs: [1, \"mdc-floating-label\", \"mat-mdc-floating-label\"],\n        hostVars: 2,\n        hostBindings: function MatFormFieldFloatingLabel_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-floating-label--float-above\", ctx.floating);\n          }\n        },\n        inputs: {\n          floating: \"floating\",\n          monitorResize: \"monitorResize\"\n        }\n      });\n    }\n  }\n  return MatFormFieldFloatingLabel;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n  const clone = htmlEl.cloneNode(true);\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nlet MatFormFieldLineRipple = /*#__PURE__*/(() => {\n  class MatFormFieldLineRipple {\n    constructor(_elementRef, ngZone) {\n      this._elementRef = _elementRef;\n      this._handleTransitionEnd = event => {\n        const classList = this._elementRef.nativeElement.classList;\n        const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n        if (event.propertyName === 'opacity' && isDeactivating) {\n          classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n        }\n      };\n      ngZone.runOutsideAngular(() => {\n        _elementRef.nativeElement.addEventListener('transitionend', this._handleTransitionEnd);\n      });\n    }\n    activate() {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove(DEACTIVATING_CLASS);\n      classList.add(ACTIVATE_CLASS);\n    }\n    deactivate() {\n      this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n    }\n    ngOnDestroy() {\n      this._elementRef.nativeElement.removeEventListener('transitionend', this._handleTransitionEnd);\n    }\n    static {\n      this.ɵfac = function MatFormFieldLineRipple_Factory(t) {\n        return new (t || MatFormFieldLineRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatFormFieldLineRipple,\n        selectors: [[\"div\", \"matFormFieldLineRipple\", \"\"]],\n        hostAttrs: [1, \"mdc-line-ripple\"]\n      });\n    }\n  }\n  return MatFormFieldLineRipple;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nlet MatFormFieldNotchedOutline = /*#__PURE__*/(() => {\n  class MatFormFieldNotchedOutline {\n    constructor(_elementRef, _ngZone) {\n      this._elementRef = _elementRef;\n      this._ngZone = _ngZone;\n      /** Whether the notch should be opened. */\n      this.open = false;\n    }\n    ngAfterViewInit() {\n      const label = this._elementRef.nativeElement.querySelector('.mdc-floating-label');\n      if (label) {\n        this._elementRef.nativeElement.classList.add('mdc-notched-outline--upgraded');\n        if (typeof requestAnimationFrame === 'function') {\n          label.style.transitionDuration = '0s';\n          this._ngZone.runOutsideAngular(() => {\n            requestAnimationFrame(() => label.style.transitionDuration = '');\n          });\n        }\n      } else {\n        this._elementRef.nativeElement.classList.add('mdc-notched-outline--no-label');\n      }\n    }\n    _setNotchWidth(labelWidth) {\n      if (!this.open || !labelWidth) {\n        this._notch.nativeElement.style.width = '';\n      } else {\n        const NOTCH_ELEMENT_PADDING = 8;\n        const NOTCH_ELEMENT_BORDER = 1;\n        this._notch.nativeElement.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n      }\n    }\n    static {\n      this.ɵfac = function MatFormFieldNotchedOutline_Factory(t) {\n        return new (t || MatFormFieldNotchedOutline)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatFormFieldNotchedOutline,\n        selectors: [[\"div\", \"matFormFieldNotchedOutline\", \"\"]],\n        viewQuery: function MatFormFieldNotchedOutline_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notch = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mdc-notched-outline\"],\n        hostVars: 2,\n        hostBindings: function MatFormFieldNotchedOutline_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-notched-outline--notched\", ctx.open);\n          }\n        },\n        inputs: {\n          open: [\"matFormFieldNotchedOutlineOpen\", \"open\"]\n        },\n        attrs: _c1,\n        ngContentSelectors: _c2,\n        decls: 5,\n        vars: 0,\n        consts: [[1, \"mdc-notched-outline__leading\"], [1, \"mdc-notched-outline__notch\"], [\"notch\", \"\"], [1, \"mdc-notched-outline__trailing\"]],\n        template: function MatFormFieldNotchedOutline_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelement(0, \"div\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1, 2);\n            i0.ɵɵprojection(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(4, \"div\", 3);\n          }\n        },\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatFormFieldNotchedOutline;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n */\nconst matFormFieldAnimations = {\n  /** Animation that transitions the form field's error and hint messages. */\n  transitionMessages: /*#__PURE__*/trigger('transitionMessages', [\n  /*#__PURE__*/\n  // TODO(mmalerba): Use angular animations for label animation as well.\n  state('enter', /*#__PURE__*/style({\n    opacity: 1,\n    transform: 'translateY(0%)'\n  })), /*#__PURE__*/transition('void => enter', [/*#__PURE__*/style({\n    opacity: 0,\n    transform: 'translateY(-5px)'\n  }), /*#__PURE__*/animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nlet MatFormFieldControl = /*#__PURE__*/(() => {\n  class MatFormFieldControl {\n    static {\n      this.ɵfac = function MatFormFieldControl_Factory(t) {\n        return new (t || MatFormFieldControl)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatFormFieldControl\n      });\n    }\n  }\n  return MatFormFieldControl;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = /*#__PURE__*/new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\nlet nextUniqueId = 0;\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nlet MatFormField = /*#__PURE__*/(() => {\n  class MatFormField {\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n      return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n      this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    /** Whether the label should always float or float as the user types. */\n    get floatLabel() {\n      return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n    }\n    set floatLabel(value) {\n      if (value !== this._floatLabel) {\n        this._floatLabel = value;\n        // For backwards compatibility. Custom form field controls or directives might set\n        // the \"floatLabel\" input and expect the form field view to be updated automatically.\n        // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n        // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** The form field appearance style. */\n    get appearance() {\n      return this._appearance;\n    }\n    set appearance(value) {\n      const oldValue = this._appearance;\n      const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n          throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n        }\n      }\n      this._appearance = newAppearance;\n      if (this._appearance === 'outline' && this._appearance !== oldValue) {\n        // If the appearance has been switched to `outline`, the label offset needs to be updated.\n        // The update can happen once the view has been re-checked, but not immediately because\n        // the view has not been updated and the notched-outline floating label is not present.\n        this._needsOutlineLabelOffsetUpdateOnStable = true;\n      }\n    }\n    /**\n     * Whether the form field should reserve space for one line of hint/error text (default)\n     * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n     * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n     */\n    get subscriptSizing() {\n      return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    set subscriptSizing(value) {\n      this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    /** Text for the form field hint. */\n    get hintLabel() {\n      return this._hintLabel;\n    }\n    set hintLabel(value) {\n      this._hintLabel = value;\n      this._processHints();\n    }\n    /** Gets the current form field control */\n    get _control() {\n      return this._explicitFormFieldControl || this._formFieldControl;\n    }\n    set _control(value) {\n      this._explicitFormFieldControl = value;\n    }\n    constructor(_elementRef, _changeDetectorRef, _ngZone, _dir, _platform, _defaults, _animationMode,\n    /**\n     * @deprecated not needed, to be removed.\n     * @breaking-change 17.0.0 remove this param\n     */\n    _unusedDocument) {\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._ngZone = _ngZone;\n      this._dir = _dir;\n      this._platform = _platform;\n      this._defaults = _defaults;\n      this._animationMode = _animationMode;\n      this._hideRequiredMarker = false;\n      /** The color palette for the form field. */\n      this.color = 'primary';\n      this._appearance = DEFAULT_APPEARANCE;\n      this._subscriptSizing = null;\n      this._hintLabel = '';\n      this._hasIconPrefix = false;\n      this._hasTextPrefix = false;\n      this._hasIconSuffix = false;\n      this._hasTextSuffix = false;\n      // Unique id for the internal form field label.\n      this._labelId = `mat-mdc-form-field-label-${nextUniqueId++}`;\n      // Unique id for the hint label.\n      this._hintLabelId = `mat-mdc-hint-${nextUniqueId++}`;\n      /** State of the mat-hint and mat-error animations. */\n      this._subscriptAnimationState = '';\n      this._destroyed = new Subject();\n      this._isFocused = null;\n      this._needsOutlineLabelOffsetUpdateOnStable = false;\n      if (_defaults) {\n        if (_defaults.appearance) {\n          this.appearance = _defaults.appearance;\n        }\n        this._hideRequiredMarker = Boolean(_defaults?.hideRequiredMarker);\n        if (_defaults.color) {\n          this.color = _defaults.color;\n        }\n      }\n    }\n    ngAfterViewInit() {\n      // Initial focus state sync. This happens rarely, but we want to account for\n      // it in case the form field control has \"focused\" set to true on init.\n      this._updateFocusState();\n      // Enable animations now. This ensures we don't animate on initial render.\n      this._subscriptAnimationState = 'enter';\n      // Because the above changes a value used in the template after it was checked, we need\n      // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n      this._changeDetectorRef.detectChanges();\n    }\n    ngAfterContentInit() {\n      this._assertFormFieldControl();\n      this._initializeControl();\n      this._initializeSubscript();\n      this._initializePrefixAndSuffix();\n      this._initializeOutlineLabelOffsetSubscriptions();\n    }\n    ngAfterContentChecked() {\n      this._assertFormFieldControl();\n    }\n    ngOnDestroy() {\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId() {\n      return this._hasFloatingLabel() ? this._labelId : null;\n    }\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form field\n     * should be positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n      return this._textField || this._elementRef;\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n      // This is for backwards compatibility only. Consumers of the form field might use\n      // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n      // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n      // animation. This is different in MDC where the label always animates, so this method\n      // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n      // the floating label state without animations. The non-MDC implementation was inconsistent\n      // because it always animates if \"floatLabel\" is set away from \"always\".\n      // TODO(devversion): consider removing this method when releasing the MDC form field.\n      if (this._hasFloatingLabel()) {\n        this.floatLabel = 'always';\n      }\n    }\n    /** Initializes the registered form field control. */\n    _initializeControl() {\n      const control = this._control;\n      if (control.controlType) {\n        this._elementRef.nativeElement.classList.add(`mat-mdc-form-field-type-${control.controlType}`);\n      }\n      // Subscribe to changes in the child control state in order to update the form field UI.\n      control.stateChanges.subscribe(() => {\n        this._updateFocusState();\n        this._syncDescribedByIds();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Run change detection if the value changes.\n      if (control.ngControl && control.ngControl.valueChanges) {\n        control.ngControl.valueChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n      }\n    }\n    _checkPrefixAndSuffixTypes() {\n      this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n      this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n      this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n      this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n    }\n    /** Initializes the prefix and suffix containers. */\n    _initializePrefixAndSuffix() {\n      this._checkPrefixAndSuffixTypes();\n      // Mark the form field as dirty whenever the prefix or suffix children change. This\n      // is necessary because we conditionally display the prefix/suffix containers based\n      // on whether there is projected content.\n      merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n        this._checkPrefixAndSuffixTypes();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /**\n     * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n     * with the custom form field control. Also subscribes to hint and error changes in order\n     * to be able to validate and synchronize ids on change.\n     */\n    _initializeSubscript() {\n      // Re-validate when the number of hints changes.\n      this._hintChildren.changes.subscribe(() => {\n        this._processHints();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Update the aria-described by when the number of errors changes.\n      this._errorChildren.changes.subscribe(() => {\n        this._syncDescribedByIds();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Initial mat-hint validation and subscript describedByIds sync.\n      this._validateHints();\n      this._syncDescribedByIds();\n    }\n    /** Throws an error if the form field's control is missing. */\n    _assertFormFieldControl() {\n      if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatFormFieldMissingControlError();\n      }\n    }\n    _updateFocusState() {\n      // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n      // certain DOM events are emitted. This is not possible in our implementation of the\n      // form field because we support abstract form field controls which are not necessarily\n      // of type input, nor do we have a reference to a native form field control element. Instead\n      // we handle the focus by checking if the abstract form field control focused state changes.\n      if (this._control.focused && !this._isFocused) {\n        this._isFocused = true;\n        this._lineRipple?.activate();\n      } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n        this._isFocused = false;\n        this._lineRipple?.deactivate();\n      }\n      this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n    }\n    /**\n     * The floating label in the docked state needs to account for prefixes. The horizontal offset\n     * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n     * form field is added to the DOM. This method sets up all subscriptions which are needed to\n     * trigger the label offset update. In general, we want to avoid performing measurements often,\n     * so we rely on the `NgZone` as indicator when the offset should be recalculated, instead of\n     * checking every change detection cycle.\n     */\n    _initializeOutlineLabelOffsetSubscriptions() {\n      // Whenever the prefix changes, schedule an update of the label offset.\n      this._prefixChildren.changes.subscribe(() => this._needsOutlineLabelOffsetUpdateOnStable = true);\n      // Note that we have to run outside of the `NgZone` explicitly, in order to avoid\n      // throwing users into an infinite loop if `zone-patch-rxjs` is included.\n      this._ngZone.runOutsideAngular(() => {\n        this._ngZone.onStable.pipe(takeUntil(this._destroyed)).subscribe(() => {\n          if (this._needsOutlineLabelOffsetUpdateOnStable) {\n            this._needsOutlineLabelOffsetUpdateOnStable = false;\n            this._updateOutlineLabelOffset();\n          }\n        });\n      });\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._needsOutlineLabelOffsetUpdateOnStable = true);\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n      return this.floatLabel === 'always';\n    }\n    _hasOutline() {\n      return this.appearance === 'outline';\n    }\n    /**\n     * Whether the label should display in the infix. Labels in the outline appearance are\n     * displayed as part of the notched-outline and are horizontally offset to account for\n     * form field prefix content. This won't work in server side rendering since we cannot\n     * measure the width of the prefix container. To make the docked label appear as if the\n     * right offset has been calculated, we forcibly render the label inside the infix. Since\n     * the label is part of the infix, the label cannot overflow the prefix content.\n     */\n    _forceDisplayInfixLabel() {\n      return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n    }\n    _hasFloatingLabel() {\n      return !!this._labelChildNonStatic || !!this._labelChildStatic;\n    }\n    _shouldLabelFloat() {\n      return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n    }\n    /**\n     * Determines whether a class from the AbstractControlDirective\n     * should be forwarded to the host element.\n     */\n    _shouldForward(prop) {\n      const control = this._control ? this._control.ngControl : null;\n      return control && control[prop];\n    }\n    /** Determines whether to display hints or errors. */\n    _getDisplayedMessages() {\n      return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState ? 'error' : 'hint';\n    }\n    /** Handle label resize events. */\n    _handleLabelResized() {\n      this._refreshOutlineNotchWidth();\n    }\n    /** Refreshes the width of the outline-notch, if present. */\n    _refreshOutlineNotchWidth() {\n      if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n        this._notchedOutline?._setNotchWidth(0);\n      } else {\n        this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n      }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n      this._validateHints();\n      this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n     * label specified set through the input is being considered as \"start\" aligned.\n     *\n     * This method is a noop if Angular runs in production mode.\n     */\n    _validateHints() {\n      if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        let startHint;\n        let endHint;\n        this._hintChildren.forEach(hint => {\n          if (hint.align === 'start') {\n            if (startHint || this.hintLabel) {\n              throw getMatFormFieldDuplicatedHintError('start');\n            }\n            startHint = hint;\n          } else if (hint.align === 'end') {\n            if (endHint) {\n              throw getMatFormFieldDuplicatedHintError('end');\n            }\n            endHint = hint;\n          }\n        });\n      }\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n      if (this._control) {\n        let ids = [];\n        // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n        if (this._control.userAriaDescribedBy && typeof this._control.userAriaDescribedBy === 'string') {\n          ids.push(...this._control.userAriaDescribedBy.split(' '));\n        }\n        if (this._getDisplayedMessages() === 'hint') {\n          const startHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'start') : null;\n          const endHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'end') : null;\n          if (startHint) {\n            ids.push(startHint.id);\n          } else if (this._hintLabel) {\n            ids.push(this._hintLabelId);\n          }\n          if (endHint) {\n            ids.push(endHint.id);\n          }\n        } else if (this._errorChildren) {\n          ids.push(...this._errorChildren.map(error => error.id));\n        }\n        this._control.setDescribedByIds(ids);\n      }\n    }\n    /**\n     * Updates the horizontal offset of the label in the outline appearance. In the outline\n     * appearance, the notched-outline and label are not relative to the infix container because\n     * the outline intends to surround prefixes, suffixes and the infix. This means that the\n     * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n     * horizontally offset the label by the width of the prefix container. The MDC text-field does\n     * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n     * incorporate the horizontal offset into their default text-field styles.\n     */\n    _updateOutlineLabelOffset() {\n      if (!this._platform.isBrowser || !this._hasOutline() || !this._floatingLabel) {\n        return;\n      }\n      const floatingLabel = this._floatingLabel.element;\n      // If no prefix is displayed, reset the outline label offset from potential\n      // previous label offset updates.\n      if (!(this._iconPrefixContainer || this._textPrefixContainer)) {\n        floatingLabel.style.transform = '';\n        return;\n      }\n      // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n      // the label offset update until the zone stabilizes.\n      if (!this._isAttachedToDom()) {\n        this._needsOutlineLabelOffsetUpdateOnStable = true;\n        return;\n      }\n      const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n      const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n      const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n      const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n      // If the directionality is RTL, the x-axis transform needs to be inverted. This\n      // is because `transformX` does not change based on the page directionality.\n      const negate = this._dir.value === 'rtl' ? '-1' : '1';\n      const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n      const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n      const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n      // Update the translateX of the floating label to account for the prefix container,\n      // but allow the CSS to override this setting via a CSS variable when the label is\n      // floating.\n      floatingLabel.style.transform = `var(\n        --mat-mdc-form-field-label-transform,\n        ${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset})\n    )`;\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDom() {\n      const element = this._elementRef.nativeElement;\n      if (element.getRootNode) {\n        const rootNode = element.getRootNode();\n        // If the element is inside the DOM the root node will be either the document\n        // or the closest shadow root, otherwise it'll be the element itself.\n        return rootNode && rootNode !== element;\n      }\n      // Otherwise fall back to checking if it's in the document. This doesn't account for\n      // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n      return document.documentElement.contains(element);\n    }\n    static {\n      this.ɵfac = function MatFormField_Factory(t) {\n        return new (t || MatFormField)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.Directionality), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(MAT_FORM_FIELD_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatFormField,\n        selectors: [[\"mat-form-field\"]],\n        contentQueries: function MatFormField_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatLabel, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatLabel, 7);\n            i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 5);\n            i0.ɵɵcontentQuery(dirIndex, MAT_PREFIX, 5);\n            i0.ɵɵcontentQuery(dirIndex, MAT_SUFFIX, 5);\n            i0.ɵɵcontentQuery(dirIndex, MAT_ERROR, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatHint, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelChildNonStatic = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelChildStatic = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._formFieldControl = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prefixChildren = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._suffixChildren = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._errorChildren = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._hintChildren = _t);\n          }\n        },\n        viewQuery: function MatFormField_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c3, 5);\n            i0.ɵɵviewQuery(_c4, 5);\n            i0.ɵɵviewQuery(_c5, 5);\n            i0.ɵɵviewQuery(MatFormFieldFloatingLabel, 5);\n            i0.ɵɵviewQuery(MatFormFieldNotchedOutline, 5);\n            i0.ɵɵviewQuery(MatFormFieldLineRipple, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textField = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconPrefixContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textPrefixContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._floatingLabel = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notchedOutline = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lineRipple = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-form-field\"],\n        hostVars: 42,\n        hostBindings: function MatFormField_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-form-field-label-always-float\", ctx._shouldAlwaysFloat())(\"mat-mdc-form-field-has-icon-prefix\", ctx._hasIconPrefix)(\"mat-mdc-form-field-has-icon-suffix\", ctx._hasIconSuffix)(\"mat-form-field-invalid\", ctx._control.errorState)(\"mat-form-field-disabled\", ctx._control.disabled)(\"mat-form-field-autofilled\", ctx._control.autofilled)(\"mat-form-field-no-animations\", ctx._animationMode === \"NoopAnimations\")(\"mat-form-field-appearance-fill\", ctx.appearance == \"fill\")(\"mat-form-field-appearance-outline\", ctx.appearance == \"outline\")(\"mat-form-field-hide-placeholder\", ctx._hasFloatingLabel() && !ctx._shouldLabelFloat())(\"mat-focused\", ctx._control.focused)(\"mat-primary\", ctx.color !== \"accent\" && ctx.color !== \"warn\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"ng-untouched\", ctx._shouldForward(\"untouched\"))(\"ng-touched\", ctx._shouldForward(\"touched\"))(\"ng-pristine\", ctx._shouldForward(\"pristine\"))(\"ng-dirty\", ctx._shouldForward(\"dirty\"))(\"ng-valid\", ctx._shouldForward(\"valid\"))(\"ng-invalid\", ctx._shouldForward(\"invalid\"))(\"ng-pending\", ctx._shouldForward(\"pending\"));\n          }\n        },\n        inputs: {\n          hideRequiredMarker: \"hideRequiredMarker\",\n          color: \"color\",\n          floatLabel: \"floatLabel\",\n          appearance: \"appearance\",\n          subscriptSizing: \"subscriptSizing\",\n          hintLabel: \"hintLabel\"\n        },\n        exportAs: [\"matFormField\"],\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MAT_FORM_FIELD,\n          useExisting: MatFormField\n        }, {\n          provide: FLOATING_LABEL_PARENT,\n          useExisting: MatFormField\n        }])],\n        ngContentSelectors: _c7,\n        decls: 18,\n        vars: 23,\n        consts: [[\"labelTemplate\", \"\"], [1, \"mat-mdc-text-field-wrapper\", \"mdc-text-field\", 3, \"click\"], [\"textField\", \"\"], [\"class\", \"mat-mdc-form-field-focus-overlay\", 4, \"ngIf\"], [1, \"mat-mdc-form-field-flex\"], [\"matFormFieldNotchedOutline\", \"\", 3, \"matFormFieldNotchedOutlineOpen\", 4, \"ngIf\"], [\"class\", \"mat-mdc-form-field-icon-prefix\", 4, \"ngIf\"], [\"class\", \"mat-mdc-form-field-text-prefix\", 4, \"ngIf\"], [1, \"mat-mdc-form-field-infix\"], [3, \"ngIf\"], [\"class\", \"mat-mdc-form-field-text-suffix\", 4, \"ngIf\"], [\"class\", \"mat-mdc-form-field-icon-suffix\", 4, \"ngIf\"], [\"matFormFieldLineRipple\", \"\", 4, \"ngIf\"], [1, \"mat-mdc-form-field-subscript-wrapper\", \"mat-mdc-form-field-bottom-align\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-form-field-error-wrapper\", 4, \"ngSwitchCase\"], [\"class\", \"mat-mdc-form-field-hint-wrapper\", 4, \"ngSwitchCase\"], [\"matFormFieldFloatingLabel\", \"\", 3, \"floating\", \"monitorResize\", \"id\", 4, \"ngIf\"], [\"matFormFieldFloatingLabel\", \"\", 3, \"floating\", \"monitorResize\", \"id\"], [\"aria-hidden\", \"true\", \"class\", \"mat-mdc-form-field-required-marker mdc-floating-label--required\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-form-field-required-marker\", \"mdc-floating-label--required\"], [1, \"mat-mdc-form-field-focus-overlay\"], [\"matFormFieldNotchedOutline\", \"\", 3, \"matFormFieldNotchedOutlineOpen\"], [3, \"ngTemplateOutlet\"], [1, \"mat-mdc-form-field-icon-prefix\"], [\"iconPrefixContainer\", \"\"], [1, \"mat-mdc-form-field-text-prefix\"], [\"textPrefixContainer\", \"\"], [1, \"mat-mdc-form-field-text-suffix\"], [1, \"mat-mdc-form-field-icon-suffix\"], [\"matFormFieldLineRipple\", \"\"], [1, \"mat-mdc-form-field-error-wrapper\"], [1, \"mat-mdc-form-field-hint-wrapper\"], [3, \"id\", 4, \"ngIf\"], [1, \"mat-mdc-form-field-hint-spacer\"], [3, \"id\"]],\n        template: function MatFormField_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c6);\n            i0.ɵɵtemplate(0, MatFormField_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementStart(2, \"div\", 1, 2);\n            i0.ɵɵlistener(\"click\", function MatFormField_Template_div_click_2_listener($event) {\n              return ctx._control.onContainerClick($event);\n            });\n            i0.ɵɵtemplate(4, MatFormField_div_4_Template, 1, 0, \"div\", 3);\n            i0.ɵɵelementStart(5, \"div\", 4);\n            i0.ɵɵtemplate(6, MatFormField_div_6_Template, 2, 2, \"div\", 5);\n            i0.ɵɵtemplate(7, MatFormField_div_7_Template, 3, 0, \"div\", 6);\n            i0.ɵɵtemplate(8, MatFormField_div_8_Template, 3, 0, \"div\", 7);\n            i0.ɵɵelementStart(9, \"div\", 8);\n            i0.ɵɵtemplate(10, MatFormField_ng_template_10_Template, 1, 1, \"ng-template\", 9);\n            i0.ɵɵprojection(11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(12, MatFormField_div_12_Template, 2, 0, \"div\", 10);\n            i0.ɵɵtemplate(13, MatFormField_div_13_Template, 2, 0, \"div\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(14, MatFormField_div_14_Template, 1, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 13);\n            i0.ɵɵtemplate(16, MatFormField_div_16_Template, 2, 1, \"div\", 14);\n            i0.ɵɵtemplate(17, MatFormField_div_17_Template, 5, 2, \"div\", 15);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"mdc-text-field--filled\", !ctx._hasOutline())(\"mdc-text-field--outlined\", ctx._hasOutline())(\"mdc-text-field--no-label\", !ctx._hasFloatingLabel())(\"mdc-text-field--disabled\", ctx._control.disabled)(\"mdc-text-field--invalid\", ctx._control.errorState);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx._hasOutline() && !ctx._control.disabled);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx._hasOutline());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx._hasIconPrefix);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx._hasTextPrefix);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx._hasOutline() || ctx._forceDisplayInfixLabel());\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx._hasTextSuffix);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx._hasIconSuffix);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx._hasOutline());\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"mat-mdc-form-field-subscript-dynamic-size\", ctx.subscriptSizing === \"dynamic\");\n            i0.ɵɵproperty(\"ngSwitch\", ctx._getDisplayedMessages());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", \"error\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", \"hint\");\n          }\n        },\n        dependencies: [i3.NgIf, i3.NgTemplateOutlet, i3.NgSwitch, i3.NgSwitchCase, MatHint, MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, MatFormFieldLineRipple],\n        styles: [\".mdc-text-field{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0;display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-text-field__input{height:28px;width:100%;min-width:0;border:none;border-radius:0;background:none;appearance:none;padding:0}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}@media all{.mdc-text-field__input::placeholder{opacity:0}}@media all{.mdc-text-field__input:-ms-input-placeholder{opacity:0}}@media all{.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}}@media all{.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}}.mdc-text-field__affix{height:28px;opacity:0;white-space:nowrap}.mdc-text-field--label-floating .mdc-text-field__affix,.mdc-text-field--no-label .mdc-text-field__affix{opacity:1}@supports(-webkit-hyphens: none){.mdc-text-field--outlined .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field__affix--prefix,.mdc-text-field__affix--prefix[dir=rtl]{padding-left:2px;padding-right:0}.mdc-text-field--end-aligned .mdc-text-field__affix--prefix{padding-left:0;padding-right:12px}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir=rtl]{padding-left:12px;padding-right:0}.mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field__affix--suffix,.mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:12px}.mdc-text-field--end-aligned .mdc-text-field__affix--suffix{padding-left:2px;padding-right:0}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:2px}.mdc-text-field--filled{height:56px}.mdc-text-field--filled::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}.mdc-text-field--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-text-field--filled .mdc-floating-label,.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled.mdc-text-field--no-label::before{display:none}@supports(-webkit-hyphens: none){.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field--outlined{height:56px;overflow:visible}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--outlined .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px, var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px, var(--mdc-shape-small, 4px))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px, var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px, var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px, var(--mdc-shape-small, 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px, var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:initial}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mdc-text-field--textarea{flex-direction:column;align-items:center;width:auto;height:auto;padding:0}.mdc-text-field--textarea .mdc-floating-label{top:19px}.mdc-text-field--textarea .mdc-floating-label:not(.mdc-floating-label--float-above){transform:none}.mdc-text-field--textarea .mdc-text-field__input{flex-grow:1;height:auto;min-height:1.5rem;overflow-x:hidden;overflow-y:auto;box-sizing:border-box;resize:none;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--filled::before{display:none}.mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-10.25px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input{margin-top:23px;margin-bottom:9px}.mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label{top:18px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field__input{margin-bottom:2px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter{align-self:flex-end;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::after{display:inline-block;width:0;height:16px;content:\\\"\\\";vertical-align:-16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::before{display:none}.mdc-text-field__resizer{align-self:stretch;display:inline-flex;flex-direction:column;flex-grow:1;max-height:100%;max-width:100%;min-height:56px;min-width:fit-content;min-width:-moz-available;min-width:-webkit-fill-available;overflow:hidden;resize:both}.mdc-text-field--filled .mdc-text-field__resizer{transform:translateY(-1px)}.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateY(1px)}.mdc-text-field--outlined .mdc-text-field__resizer{transform:translateX(-1px) translateY(-1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer,.mdc-text-field--outlined .mdc-text-field__resizer[dir=rtl]{transform:translateX(1px) translateY(-1px)}.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateX(1px) translateY(1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input[dir=rtl],.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter[dir=rtl]{transform:translateX(-1px) translateY(1px)}.mdc-text-field--with-leading-icon{padding-left:0;padding-right:16px}[dir=rtl] .mdc-text-field--with-leading-icon,.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:16px;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 48px);left:48px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:48px}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 64px / 0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:36px}.mdc-text-field--with-leading-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--with-trailing-icon{padding-left:16px;padding-right:0}[dir=rtl] .mdc-text-field--with-trailing-icon,.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0;padding-right:16px}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 64px / 0.75)}.mdc-text-field--with-trailing-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 96px / 0.75)}.mdc-text-field-helper-line{display:flex;justify-content:space-between;box-sizing:border-box}.mdc-text-field+.mdc-text-field-helper-line{padding-right:16px;padding-left:16px}.mdc-form-field>.mdc-text-field+label{align-self:flex-start}.mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--focused .mdc-notched-outline__trailing{border-width:2px}.mdc-text-field--focused+.mdc-text-field-helper-line .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg){opacity:1}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--invalid+.mdc-text-field-helper-line .mdc-text-field-helper-text--validation-msg{opacity:1}.mdc-text-field--disabled{pointer-events:none}@media screen and (forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--disabled .mdc-floating-label{cursor:default}.mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple{display:none}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--end-aligned .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--end-aligned .mdc-text-field__input[dir=rtl]{text-align:left}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix{direction:ltr}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--leading{order:1}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{order:2}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input{order:3}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{order:4}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--trailing{order:5}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--prefix{padding-right:12px}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--suffix{padding-left:2px}.mdc-floating-label{position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0px;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after,.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;pointer-events:none}.mdc-notched-outline__trailing{flex-grow:1}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{border-top:1px solid;border-bottom:1px solid}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none}.mdc-notched-outline__notch{max-width:calc(100% - 12px * 2)}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::after{border-bottom-width:2px}.mdc-text-field--filled{--mdc-filled-text-field-active-indicator-height:1px;--mdc-filled-text-field-focus-active-indicator-height:2px;--mdc-filled-text-field-container-shape:4px;border-top-left-radius:var(--mdc-filled-text-field-container-shape);border-top-right-radius:var(--mdc-filled-text-field-container-shape);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-caret-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-focus-label-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-disabled-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-focus-label-text-color)}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font);font-size:var(--mdc-filled-text-field-label-text-size);font-weight:var(--mdc-filled-text-field-label-text-weight);letter-spacing:var(--mdc-filled-text-field-label-text-tracking)}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color)}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color)}.mdc-text-field--filled .mdc-line-ripple::before{border-bottom-width:var(--mdc-filled-text-field-active-indicator-height)}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height)}.mdc-text-field--outlined{--mdc-outlined-text-field-outline-width:1px;--mdc-outlined-text-field-focus-outline-width:2px;--mdc-outlined-text-field-container-shape:4px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-caret-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-focus-label-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-disabled-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-focus-label-text-color)}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font);font-size:var(--mdc-outlined-text-field-label-text-size);font-weight:var(--mdc-outlined-text-field-label-text-weight);letter-spacing:var(--mdc-outlined-text-field-label-text-tracking)}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px, var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px, var(--mdc-outlined-text-field-container-shape))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px, calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px, var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px, calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px, var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px, var(--mdc-outlined-text-field-container-shape))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px, var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px, calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px, calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-hover-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-focus-outline-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-disabled-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-hover-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-focus-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-outline-width)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-focus-outline-width)}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-text-field-wrapper::before{content:none}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}.cdk-high-contrast-active .mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font);line-height:var(--mat-form-field-container-text-line-height);font-size:var(--mat-form-field-container-text-size);letter-spacing:var(--mat-form-field-container-text-tracking);font-weight:var(--mat-form-field-container-text-weight)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size) * var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:12px;box-sizing:content-box}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__affix{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled.mdc-ripple-upgraded--background-focused .mdc-text-field__ripple::before,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea{transition:none}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-filled 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-filled{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon{0%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}[dir=rtl] .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl{0%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matFormFieldAnimations.transitionMessages]\n        },\n        changeDetection: 0\n      });\n    }\n  }\n  return MatFormField;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatFormFieldModule = /*#__PURE__*/(() => {\n  class MatFormFieldModule {\n    static {\n      this.ɵfac = function MatFormFieldModule_Factory(t) {\n        return new (t || MatFormFieldModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatFormFieldModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, CommonModule, ObserversModule, MatCommonModule]\n      });\n    }\n  }\n  return MatFormFieldModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_ERROR, MAT_FORM_FIELD, MAT_FORM_FIELD_DEFAULT_OPTIONS, MAT_PREFIX, MAT_SUFFIX, MatError, MatFormField, MatFormFieldControl, MatFormFieldModule, MatHint, MatLabel, MatPrefix, MatSuffix, getMatFormFieldDuplicatedHintError, getMatFormFieldMissingControlError, getMatFormFieldPlaceholderConflictError, matFormFieldAnimations };\n//# sourceMappingURL=form-field.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}