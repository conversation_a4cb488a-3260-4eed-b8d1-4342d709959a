{"ast": null, "code": "import * as i1 from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { inject, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, InjectionToken, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { mixinColor, mixinDisabled, mixinDisableRipple, MatRippleLoader, MatCommonModule, MatRippleModule } from '@angular/material/core';\n\n/** Inputs common to all buttons. */\nconst _c0 = [\"mat-button\", \"\"];\nconst _c1 = [[[\"\", 8, \"material-icons\", 3, \"iconPositionEnd\", \"\"], [\"mat-icon\", 3, \"iconPositionEnd\", \"\"], [\"\", \"matButtonIcon\", \"\", 3, \"iconPositionEnd\", \"\"]], \"*\", [[\"\", \"iconPositionEnd\", \"\", 8, \"material-icons\"], [\"mat-icon\", \"iconPositionEnd\", \"\"], [\"\", \"matButtonIcon\", \"\", \"iconPositionEnd\", \"\"]]];\nconst _c2 = [\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\", \"*\", \".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\"];\nconst _c3 = \".mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0)}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}[dir=rtl] .mdc-button .mdc-button__icon,.mdc-button .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:0}.mdc-button .mdc-button__progress-indicator{font-size:0;position:absolute;transform:translate(-50%, -50%);top:50%;left:50%;line-height:initial}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(\\n      100% + 4px\\n    );width:calc(\\n      100% + 4px\\n    );display:none}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring{border-color:CanvasText}}.mdc-button .mdc-button__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring::after{border-color:CanvasText}}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{display:block}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}[dir=rtl] .mdc-button__label+.mdc-button__icon,.mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:0;margin-right:8px}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button{height:var(--mdc-text-button-container-height, 36px);border-radius:var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, inherit)}.mat-mdc-button:disabled{color:var(--mdc-text-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-unelevated-button{height:var(--mdc-filled-button-container-height, 36px);border-radius:var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-unelevated-button:not(:disabled){background-color:var(--mdc-filled-button-container-color, transparent)}.mat-mdc-unelevated-button:disabled{background-color:var(--mdc-filled-button-disabled-container-color, rgba(0, 0, 0, 0.12))}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, inherit)}.mat-mdc-unelevated-button:disabled{color:var(--mdc-filled-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-raised-button{height:var(--mdc-protected-button-container-height, 36px);border-radius:var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px));box-shadow:var(--mdc-protected-button-container-elevation, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:not(:disabled){background-color:var(--mdc-protected-button-container-color, transparent)}.mat-mdc-raised-button:disabled{background-color:var(--mdc-protected-button-disabled-container-color, rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, inherit)}.mat-mdc-raised-button:disabled{color:var(--mdc-protected-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-raised-button.mdc-ripple-upgraded--background-focused,.mat-mdc-raised-button:not(.mdc-ripple-upgraded):focus{box-shadow:var(--mdc-protected-button-focus-container-elevation, 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation, 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:not(:disabled):active{box-shadow:var(--mdc-protected-button-pressed-container-elevation, 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-outlined-button{height:var(--mdc-outlined-button-container-height, 36px);border-radius:var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px));padding:0 15px 0 15px;border-width:var(--mdc-outlined-button-outline-width, 1px)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, inherit)}.mat-mdc-outlined-button:disabled{color:var(--mdc-outlined-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-outlined-button .mdc-button__ripple{border-radius:var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-outlined-button:not(:disabled){border-color:var(--mdc-outlined-button-outline-color, rgba(0, 0, 0, 0.12))}.mat-mdc-outlined-button:disabled{border-color:var(--mdc-outlined-button-disabled-outline-color, rgba(0, 0, 0, 0.12))}.mat-mdc-outlined-button.mdc-button--icon-trailing{padding:0 11px 0 15px}.mat-mdc-outlined-button.mdc-button--icon-leading{padding:0 15px 0 11px}.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:var(--mdc-outlined-button-outline-width, 1px)}.mat-mdc-outlined-button .mdc-button__touch{left:calc(-1 * var(--mdc-outlined-button-outline-width, 1px));width:calc(100% + 2 * var(--mdc-outlined-button-outline-width, 1px))}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0;background-color:var(--mat-mdc-button-persistent-ripple-color)}.mat-mdc-button .mat-ripple-element,.mat-mdc-unelevated-button .mat-ripple-element,.mat-mdc-raised-button .mat-ripple-element,.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-mdc-button-ripple-color)}.mat-mdc-button .mdc-button__label,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-outlined-button .mdc-button__label{z-index:1}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-button[disabled],.mat-mdc-unelevated-button[disabled],.mat-mdc-raised-button[disabled],.mat-mdc-outlined-button[disabled]{cursor:default;pointer-events:none}.mat-mdc-button .mat-mdc-button-touch-target,.mat-mdc-unelevated-button .mat-mdc-button-touch-target,.mat-mdc-raised-button .mat-mdc-button-touch-target,.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}[dir=rtl] .mat-mdc-button>.mat-icon,.mat-mdc-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:0}.mat-mdc-button .mdc-button__label+.mat-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon,.mat-mdc-button .mdc-button__label+.mat-icon[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem;margin-left:-4px;margin-right:8px}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon,[dir=rtl] .mat-mdc-raised-button>.mat-icon,[dir=rtl] .mat-mdc-outlined-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon[dir=rtl],.mat-mdc-raised-button>.mat-icon[dir=rtl],.mat-mdc-outlined-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:0}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon,[dir=rtl] .mat-mdc-raised-button>.mat-icon,[dir=rtl] .mat-mdc-outlined-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon[dir=rtl],.mat-mdc-raised-button>.mat-icon[dir=rtl],.mat-mdc-outlined-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:-4px}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon,.mat-mdc-raised-button .mdc-button__label+.mat-icon,.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-left:8px;margin-right:-4px}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon,.mat-mdc-unelevated-button .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-raised-button .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-outlined-button .mdc-button__label+.mat-icon[dir=rtl]{margin-left:-4px;margin-right:8px}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px) * -1)}\";\nconst _c4 = \".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}\";\nconst _c5 = [\"mat-fab\", \"\"];\nconst _c6 = [\"mat-mini-fab\", \"\"];\nconst _c7 = \".mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color)}.mdc-fab{position:relative;display:inline-flex;position:relative;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;user-select:none;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-fab .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-fab[hidden]{display:none}.mdc-fab::-moz-focus-inner{padding:0;border:0}.mdc-fab:hover{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mdc-fab.mdc-ripple-upgraded--background-focused,.mdc-fab:not(.mdc-ripple-upgraded):focus{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mdc-fab .mdc-fab__focus-ring{position:absolute}.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(\\n      100% + 4px\\n    );width:calc(\\n      100% + 4px\\n    )}@media screen and (forced-colors: active){.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring{border-color:CanvasText}}.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring::after,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring::after,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring::after{border-color:CanvasText}}.mdc-fab:active,.mdc-fab:focus:active{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mdc-fab:active,.mdc-fab:focus{outline:none}.mdc-fab:hover{cursor:pointer}.mdc-fab>svg{width:100%}.mdc-fab--mini{width:40px;height:40px}.mdc-fab--extended{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-button-font-family);font-size:var(--mdc-typography-button-font-size);line-height:var(--mdc-typography-button-line-height);font-weight:var(--mdc-typography-button-font-weight);letter-spacing:var(--mdc-typography-button-letter-spacing);text-decoration:var(--mdc-typography-button-text-decoration);text-transform:var(--mdc-typography-button-text-transform);border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;height:48px;line-height:normal}.mdc-fab--extended .mdc-fab__ripple{border-radius:24px}.mdc-fab--extended .mdc-fab__icon{margin-left:calc(12px - 20px);margin-right:12px}[dir=rtl] .mdc-fab--extended .mdc-fab__icon,.mdc-fab--extended .mdc-fab__icon[dir=rtl]{margin-left:12px;margin-right:calc(12px - 20px)}.mdc-fab--extended .mdc-fab__label+.mdc-fab__icon{margin-left:12px;margin-right:calc(12px - 20px)}[dir=rtl] .mdc-fab--extended .mdc-fab__label+.mdc-fab__icon,.mdc-fab--extended .mdc-fab__label+.mdc-fab__icon[dir=rtl]{margin-left:calc(12px - 20px);margin-right:12px}.mdc-fab--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-fab--touch .mdc-fab__touch{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mdc-fab::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-fab::before{border-color:CanvasText}}.mdc-fab__label{justify-content:flex-start;text-overflow:ellipsis;white-space:nowrap;overflow-x:hidden;overflow-y:visible}.mdc-fab__icon{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mdc-fab .mdc-fab__icon{display:inline-flex;align-items:center;justify-content:center}.mdc-fab--exited{transform:scale(0);opacity:0;transition:opacity 15ms linear 150ms,transform 180ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-fab--exited .mdc-fab__icon{transform:scale(0);transition:transform 135ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mat-mdc-fab,.mat-mdc-mini-fab{background-color:var(--mdc-fab-container-color);--mdc-fab-container-shape:50%;--mdc-fab-icon-size:24px}.mat-mdc-fab .mdc-fab__icon,.mat-mdc-mini-fab .mdc-fab__icon{width:var(--mdc-fab-icon-size);height:var(--mdc-fab-icon-size);font-size:var(--mdc-fab-icon-size)}.mat-mdc-fab:not(:disabled) .mdc-fab__icon,.mat-mdc-mini-fab:not(:disabled) .mdc-fab__icon{color:var(--mdc-fab-icon-color)}.mat-mdc-fab:not(.mdc-fab--extended),.mat-mdc-mini-fab:not(.mdc-fab--extended){border-radius:var(--mdc-fab-container-shape)}.mat-mdc-fab:not(.mdc-fab--extended) .mdc-fab__ripple,.mat-mdc-mini-fab:not(.mdc-fab--extended) .mdc-fab__ripple{border-radius:var(--mdc-fab-container-shape)}.mat-mdc-extended-fab{font-family:var(--mdc-extended-fab-label-text-font);font-size:var(--mdc-extended-fab-label-text-size);font-weight:var(--mdc-extended-fab-label-text-weight);letter-spacing:var(--mdc-extended-fab-label-text-tracking)}.mat-mdc-fab,.mat-mdc-mini-fab{-webkit-tap-highlight-color:rgba(0,0,0,0);box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);color:var(--mat-mdc-fab-color, inherit);flex-shrink:0}.mat-mdc-fab .mat-mdc-button-ripple,.mat-mdc-fab .mat-mdc-button-persistent-ripple,.mat-mdc-fab .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab .mat-mdc-button-ripple,.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple,.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab .mat-mdc-button-ripple,.mat-mdc-mini-fab .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0;background-color:var(--mat-mdc-button-persistent-ripple-color)}.mat-mdc-fab .mat-ripple-element,.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-mdc-button-ripple-color)}.mat-mdc-fab .mdc-button__label,.mat-mdc-mini-fab .mdc-button__label{z-index:1}.mat-mdc-fab .mat-mdc-focus-indicator,.mat-mdc-mini-fab .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab:focus .mat-mdc-focus-indicator::before,.mat-mdc-mini-fab:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab .mat-mdc-button-touch-target,.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-fab._mat-animation-noopable,.mat-mdc-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab:hover,.mat-mdc-fab:focus,.mat-mdc-mini-fab:hover,.mat-mdc-mini-fab:focus{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-mdc-fab:active,.mat-mdc-fab:focus:active,.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-mdc-fab[disabled],.mat-mdc-mini-fab[disabled]{cursor:default;pointer-events:none;box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-fab:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-mini-fab:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}.mat-mdc-fab .mat-icon,.mat-mdc-fab .material-icons,.mat-mdc-mini-fab .mat-icon,.mat-mdc-mini-fab .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab .mat-mdc-focus-indicator::before,.mat-mdc-mini-fab .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:calc(12px - 20px);margin-right:12px}[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons,.mat-mdc-extended-fab>.mat-icon[dir=rtl],.mat-mdc-extended-fab>.material-icons[dir=rtl]{margin-left:12px;margin-right:calc(12px - 20px)}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons{margin-left:12px;margin-right:calc(12px - 20px)}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-extended-fab .mdc-button__label+.material-icons[dir=rtl]{margin-left:calc(12px - 20px);margin-right:12px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\";\nconst _c8 = [\"mat-icon-button\", \"\"];\nconst _c9 = [\"*\"];\nconst _c10 = \".mdc-icon-button{display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;user-select:none;z-index:0;overflow:visible}.mdc-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}@media screen and (forced-colors: active){.mdc-icon-button.mdc-ripple-upgraded--background-focused .mdc-icon-button__focus-ring,.mdc-icon-button:not(.mdc-ripple-upgraded):focus .mdc-icon-button__focus-ring{display:block}}.mdc-icon-button:disabled{cursor:default;pointer-events:none}.mdc-icon-button[hidden]{display:none}.mdc-icon-button--display-flex{align-items:center;display:inline-flex;justify-content:center}.mdc-icon-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%;display:none}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring{border-color:CanvasText}}.mdc-icon-button__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring::after{border-color:CanvasText}}.mdc-icon-button__icon{display:inline-block}.mdc-icon-button__icon.mdc-icon-button__icon--on{display:none}.mdc-icon-button--on .mdc-icon-button__icon{display:none}.mdc-icon-button--on .mdc-icon-button__icon.mdc-icon-button__icon--on{display:inline-block}.mdc-icon-button__link{height:100%;left:0;outline:none;position:absolute;top:0;width:100%}.mat-mdc-icon-button{height:var(--mdc-icon-button-state-layer-size);width:var(--mdc-icon-button-state-layer-size);color:var(--mdc-icon-button-icon-color);--mdc-icon-button-state-layer-size:48px;--mdc-icon-button-icon-size:24px;--mdc-icon-button-disabled-icon-color:black;--mdc-icon-button-disabled-icon-opacity:0.38}.mat-mdc-icon-button .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button svg,.mat-mdc-icon-button img{width:var(--mdc-icon-button-icon-size);height:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button:disabled{opacity:var(--mdc-icon-button-disabled-icon-opacity)}.mat-mdc-icon-button:disabled{color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button{padding:12px;font-size:var(--mdc-icon-button-icon-size);border-radius:50%;flex-shrink:0;text-align:center;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button svg{vertical-align:baseline}.mat-mdc-icon-button[disabled]{cursor:default;pointer-events:none;opacity:1}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0;background-color:var(--mat-mdc-button-persistent-ripple-color)}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-mdc-button-ripple-color)}.mat-mdc-icon-button .mdc-button__label{z-index:1}.mat-mdc-icon-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\";\nconst MAT_BUTTON_INPUTS = ['disabled', 'disableRipple', 'color'];\n/** Shared host configuration for all buttons */\nconst MAT_BUTTON_HOST = {\n  '[attr.disabled]': 'disabled || null',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true'\n};\n/** List of classes to add to buttons instances based on host attribute selector. */\nconst HOST_SELECTOR_MDC_CLASS_PAIR = [{\n  selector: 'mat-button',\n  mdcClasses: ['mdc-button', 'mat-mdc-button']\n}, {\n  selector: 'mat-flat-button',\n  mdcClasses: ['mdc-button', 'mdc-button--unelevated', 'mat-mdc-unelevated-button']\n}, {\n  selector: 'mat-raised-button',\n  mdcClasses: ['mdc-button', 'mdc-button--raised', 'mat-mdc-raised-button']\n}, {\n  selector: 'mat-stroked-button',\n  mdcClasses: ['mdc-button', 'mdc-button--outlined', 'mat-mdc-outlined-button']\n}, {\n  selector: 'mat-fab',\n  mdcClasses: ['mdc-fab', 'mat-mdc-fab']\n}, {\n  selector: 'mat-mini-fab',\n  mdcClasses: ['mdc-fab', 'mdc-fab--mini', 'mat-mdc-mini-fab']\n}, {\n  selector: 'mat-icon-button',\n  mdcClasses: ['mdc-icon-button', 'mat-mdc-icon-button']\n}];\n// Boilerplate for applying mixins to MatButton.\n/** @docs-private */\nconst _MatButtonMixin = /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisabled( /*#__PURE__*/mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n})));\n/** Base class for all buttons.  */\nlet MatButtonBase = /*#__PURE__*/(() => {\n  class MatButtonBase extends _MatButtonMixin {\n    /**\n     * Reference to the MatRipple instance of the button.\n     * @deprecated Considered an implementation detail. To be removed.\n     * @breaking-change 17.0.0\n     */\n    get ripple() {\n      return this._rippleLoader?.getRipple(this._elementRef.nativeElement);\n    }\n    set ripple(v) {\n      this._rippleLoader?.attachRipple(this._elementRef.nativeElement, v);\n    }\n    // We override `disableRipple` and `disabled` so we can hook into\n    // their setters and update the ripple disabled state accordingly.\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n      this._updateRippleDisabled();\n    }\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._updateRippleDisabled();\n    }\n    constructor(elementRef, _platform, _ngZone, _animationMode) {\n      super(elementRef);\n      this._platform = _platform;\n      this._ngZone = _ngZone;\n      this._animationMode = _animationMode;\n      this._focusMonitor = inject(FocusMonitor);\n      /**\n       * Handles the lazy creation of the MatButton ripple.\n       * Used to improve initial load time of large applications.\n       */\n      this._rippleLoader = inject(MatRippleLoader);\n      /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n      this._isFab = false;\n      this._disableRipple = false;\n      this._disabled = false;\n      this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n        className: 'mat-mdc-button-ripple'\n      });\n      const classList = elementRef.nativeElement.classList;\n      // For each of the variant selectors that is present in the button's host\n      // attributes, add the correct corresponding MDC classes.\n      for (const pair of HOST_SELECTOR_MDC_CLASS_PAIR) {\n        if (this._hasHostAttributes(pair.selector)) {\n          pair.mdcClasses.forEach(className => {\n            classList.add(className);\n          });\n        }\n      }\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Focuses the button. */\n    focus(_origin = 'program', options) {\n      if (_origin) {\n        this._focusMonitor.focusVia(this._elementRef.nativeElement, _origin, options);\n      } else {\n        this._elementRef.nativeElement.focus(options);\n      }\n    }\n    /** Gets whether the button has one of the given attributes. */\n    _hasHostAttributes(...attributes) {\n      return attributes.some(attribute => this._elementRef.nativeElement.hasAttribute(attribute));\n    }\n    _updateRippleDisabled() {\n      this._rippleLoader?.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n    }\n    static {\n      this.ɵfac = function MatButtonBase_Factory(t) {\n        i0.ɵɵinvalidFactory();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatButtonBase,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatButtonBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Shared inputs by buttons using the `<a>` tag */\nconst MAT_ANCHOR_INPUTS = ['disabled', 'disableRipple', 'color', 'tabIndex'];\n/** Shared host configuration for buttons using the `<a>` tag. */\nconst MAT_ANCHOR_HOST = {\n  '[attr.disabled]': 'disabled || null',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // Note that we ignore the user-specified tabindex when it's disabled for\n  // consistency with the `mat-button` applied on native buttons where even\n  // though they have an index, they're not tabbable.\n  '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n  '[attr.aria-disabled]': 'disabled.toString()',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true'\n};\n/**\n * Anchor button base.\n */\nlet MatAnchorBase = /*#__PURE__*/(() => {\n  class MatAnchorBase extends MatButtonBase {\n    constructor(elementRef, platform, ngZone, animationMode) {\n      super(elementRef, platform, ngZone, animationMode);\n      this._haltDisabledEvents = event => {\n        // A disabled button shouldn't apply any actions\n        if (this.disabled) {\n          event.preventDefault();\n          event.stopImmediatePropagation();\n        }\n      };\n    }\n    ngOnInit() {\n      this._ngZone.runOutsideAngular(() => {\n        this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n      });\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._elementRef.nativeElement.removeEventListener('click', this._haltDisabledEvents);\n    }\n    static {\n      this.ɵfac = function MatAnchorBase_Factory(t) {\n        i0.ɵɵinvalidFactory();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatAnchorBase,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatAnchorBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Material Design button component. Users interact with a button to perform an action.\n * See https://material.io/components/buttons\n *\n * The `MatButton` class applies to native button elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatButton` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\nlet MatButton = /*#__PURE__*/(() => {\n  class MatButton extends MatButtonBase {\n    constructor(elementRef, platform, ngZone, animationMode) {\n      super(elementRef, platform, ngZone, animationMode);\n    }\n    static {\n      this.ɵfac = function MatButton_Factory(t) {\n        return new (t || MatButton)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatButton,\n        selectors: [[\"button\", \"mat-button\", \"\"], [\"button\", \"mat-raised-button\", \"\"], [\"button\", \"mat-flat-button\", \"\"], [\"button\", \"mat-stroked-button\", \"\"]],\n        hostVars: 7,\n        hostBindings: function MatButton_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\"\n        },\n        exportAs: [\"matButton\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c0,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatButton_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [\".mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0)}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}[dir=rtl] .mdc-button .mdc-button__icon,.mdc-button .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:0}.mdc-button .mdc-button__progress-indicator{font-size:0;position:absolute;transform:translate(-50%, -50%);top:50%;left:50%;line-height:initial}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(\\n      100% + 4px\\n    );width:calc(\\n      100% + 4px\\n    );display:none}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring{border-color:CanvasText}}.mdc-button .mdc-button__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring::after{border-color:CanvasText}}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{display:block}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}[dir=rtl] .mdc-button__label+.mdc-button__icon,.mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:0;margin-right:8px}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button{height:var(--mdc-text-button-container-height, 36px);border-radius:var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, inherit)}.mat-mdc-button:disabled{color:var(--mdc-text-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-unelevated-button{height:var(--mdc-filled-button-container-height, 36px);border-radius:var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-unelevated-button:not(:disabled){background-color:var(--mdc-filled-button-container-color, transparent)}.mat-mdc-unelevated-button:disabled{background-color:var(--mdc-filled-button-disabled-container-color, rgba(0, 0, 0, 0.12))}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, inherit)}.mat-mdc-unelevated-button:disabled{color:var(--mdc-filled-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-raised-button{height:var(--mdc-protected-button-container-height, 36px);border-radius:var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px));box-shadow:var(--mdc-protected-button-container-elevation, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:not(:disabled){background-color:var(--mdc-protected-button-container-color, transparent)}.mat-mdc-raised-button:disabled{background-color:var(--mdc-protected-button-disabled-container-color, rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, inherit)}.mat-mdc-raised-button:disabled{color:var(--mdc-protected-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-raised-button.mdc-ripple-upgraded--background-focused,.mat-mdc-raised-button:not(.mdc-ripple-upgraded):focus{box-shadow:var(--mdc-protected-button-focus-container-elevation, 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation, 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:not(:disabled):active{box-shadow:var(--mdc-protected-button-pressed-container-elevation, 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12))}.mat-mdc-raised-button:disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-outlined-button{height:var(--mdc-outlined-button-container-height, 36px);border-radius:var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px));padding:0 15px 0 15px;border-width:var(--mdc-outlined-button-outline-width, 1px)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, inherit)}.mat-mdc-outlined-button:disabled{color:var(--mdc-outlined-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mat-mdc-outlined-button .mdc-button__ripple{border-radius:var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px))}.mat-mdc-outlined-button:not(:disabled){border-color:var(--mdc-outlined-button-outline-color, rgba(0, 0, 0, 0.12))}.mat-mdc-outlined-button:disabled{border-color:var(--mdc-outlined-button-disabled-outline-color, rgba(0, 0, 0, 0.12))}.mat-mdc-outlined-button.mdc-button--icon-trailing{padding:0 11px 0 15px}.mat-mdc-outlined-button.mdc-button--icon-leading{padding:0 15px 0 11px}.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:var(--mdc-outlined-button-outline-width, 1px)}.mat-mdc-outlined-button .mdc-button__touch{left:calc(-1 * var(--mdc-outlined-button-outline-width, 1px));width:calc(100% + 2 * var(--mdc-outlined-button-outline-width, 1px))}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0;background-color:var(--mat-mdc-button-persistent-ripple-color)}.mat-mdc-button .mat-ripple-element,.mat-mdc-unelevated-button .mat-ripple-element,.mat-mdc-raised-button .mat-ripple-element,.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-mdc-button-ripple-color)}.mat-mdc-button .mdc-button__label,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-outlined-button .mdc-button__label{z-index:1}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-button[disabled],.mat-mdc-unelevated-button[disabled],.mat-mdc-raised-button[disabled],.mat-mdc-outlined-button[disabled]{cursor:default;pointer-events:none}.mat-mdc-button .mat-mdc-button-touch-target,.mat-mdc-unelevated-button .mat-mdc-button-touch-target,.mat-mdc-raised-button .mat-mdc-button-touch-target,.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}[dir=rtl] .mat-mdc-button>.mat-icon,.mat-mdc-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:0}.mat-mdc-button .mdc-button__label+.mat-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon,.mat-mdc-button .mdc-button__label+.mat-icon[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem;margin-left:-4px;margin-right:8px}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon,[dir=rtl] .mat-mdc-raised-button>.mat-icon,[dir=rtl] .mat-mdc-outlined-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon[dir=rtl],.mat-mdc-raised-button>.mat-icon[dir=rtl],.mat-mdc-outlined-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:0}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon,[dir=rtl] .mat-mdc-raised-button>.mat-icon,[dir=rtl] .mat-mdc-outlined-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon[dir=rtl],.mat-mdc-raised-button>.mat-icon[dir=rtl],.mat-mdc-outlined-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:-4px}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon,.mat-mdc-raised-button .mdc-button__label+.mat-icon,.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-left:8px;margin-right:-4px}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon,.mat-mdc-unelevated-button .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-raised-button .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-outlined-button .mdc-button__label+.mat-icon[dir=rtl]{margin-left:-4px;margin-right:8px}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px) * -1)}\", \".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatButton;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material Design button component for anchor elements. Anchor elements are used to provide\n * links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons\n *\n * The `MatAnchor` class applies to native anchor elements and captures the appearances for\n * \"text button\", \"outlined button\", and \"contained button\" per the Material Design\n * specification. `MatAnchor` additionally captures an additional \"flat\" appearance, which matches\n * \"contained\" but without elevation.\n */\nlet MatAnchor = /*#__PURE__*/(() => {\n  class MatAnchor extends MatAnchorBase {\n    constructor(elementRef, platform, ngZone, animationMode) {\n      super(elementRef, platform, ngZone, animationMode);\n    }\n    static {\n      this.ɵfac = function MatAnchor_Factory(t) {\n        return new (t || MatAnchor)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatAnchor,\n        selectors: [[\"a\", \"mat-button\", \"\"], [\"a\", \"mat-raised-button\", \"\"], [\"a\", \"mat-flat-button\", \"\"], [\"a\", \"mat-stroked-button\", \"\"]],\n        hostVars: 9,\n        hostBindings: function MatAnchor_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString());\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\",\n          tabIndex: \"tabIndex\"\n        },\n        exportAs: [\"matButton\", \"matAnchor\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c0,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatAnchor_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [_c3, _c4],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatAnchor;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token to be used to override the default options for FAB. */\nconst MAT_FAB_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-mdc-fab-default-options', {\n  providedIn: 'root',\n  factory: MAT_FAB_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_FAB_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    // The FAB by default has its color set to accent.\n    color: 'accent'\n  };\n}\n// Default FAB configuration.\nconst defaults = /*#__PURE__*/MAT_FAB_DEFAULT_OPTIONS_FACTORY();\nlet buttonInputs = [...MAT_ANCHOR_INPUTS, 'extended'];\n/**\n * Material Design floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabButton` class has two appearances: normal and extended.\n */\nlet MatFabButton = /*#__PURE__*/(() => {\n  class MatFabButton extends MatButtonBase {\n    get extended() {\n      return this._extended;\n    }\n    set extended(value) {\n      this._extended = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, platform, ngZone, animationMode, _options) {\n      super(elementRef, platform, ngZone, animationMode);\n      this._options = _options;\n      this._isFab = true;\n      this._options = this._options || defaults;\n      this.color = this.defaultColor = this._options.color || defaults.color;\n    }\n    static {\n      this.ɵfac = function MatFabButton_Factory(t) {\n        return new (t || MatFabButton)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_FAB_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatFabButton,\n        selectors: [[\"button\", \"mat-fab\", \"\"]],\n        hostVars: 11,\n        hostBindings: function MatFabButton_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true)(\"mdc-fab--extended\", ctx.extended)(\"mat-mdc-extended-fab\", ctx.extended);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\",\n          tabIndex: \"tabIndex\",\n          extended: \"extended\"\n        },\n        exportAs: [\"matButton\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c5,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatFabButton_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [\".mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color)}.mdc-fab{position:relative;display:inline-flex;position:relative;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;user-select:none;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-fab .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-fab[hidden]{display:none}.mdc-fab::-moz-focus-inner{padding:0;border:0}.mdc-fab:hover{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mdc-fab.mdc-ripple-upgraded--background-focused,.mdc-fab:not(.mdc-ripple-upgraded):focus{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mdc-fab .mdc-fab__focus-ring{position:absolute}.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(\\n      100% + 4px\\n    );width:calc(\\n      100% + 4px\\n    )}@media screen and (forced-colors: active){.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring{border-color:CanvasText}}.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring::after,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring::after,.mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring::after{border-color:CanvasText}}.mdc-fab:active,.mdc-fab:focus:active{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mdc-fab:active,.mdc-fab:focus{outline:none}.mdc-fab:hover{cursor:pointer}.mdc-fab>svg{width:100%}.mdc-fab--mini{width:40px;height:40px}.mdc-fab--extended{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-button-font-family);font-size:var(--mdc-typography-button-font-size);line-height:var(--mdc-typography-button-line-height);font-weight:var(--mdc-typography-button-font-weight);letter-spacing:var(--mdc-typography-button-letter-spacing);text-decoration:var(--mdc-typography-button-text-decoration);text-transform:var(--mdc-typography-button-text-transform);border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;height:48px;line-height:normal}.mdc-fab--extended .mdc-fab__ripple{border-radius:24px}.mdc-fab--extended .mdc-fab__icon{margin-left:calc(12px - 20px);margin-right:12px}[dir=rtl] .mdc-fab--extended .mdc-fab__icon,.mdc-fab--extended .mdc-fab__icon[dir=rtl]{margin-left:12px;margin-right:calc(12px - 20px)}.mdc-fab--extended .mdc-fab__label+.mdc-fab__icon{margin-left:12px;margin-right:calc(12px - 20px)}[dir=rtl] .mdc-fab--extended .mdc-fab__label+.mdc-fab__icon,.mdc-fab--extended .mdc-fab__label+.mdc-fab__icon[dir=rtl]{margin-left:calc(12px - 20px);margin-right:12px}.mdc-fab--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-fab--touch .mdc-fab__touch{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mdc-fab::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-fab::before{border-color:CanvasText}}.mdc-fab__label{justify-content:flex-start;text-overflow:ellipsis;white-space:nowrap;overflow-x:hidden;overflow-y:visible}.mdc-fab__icon{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mdc-fab .mdc-fab__icon{display:inline-flex;align-items:center;justify-content:center}.mdc-fab--exited{transform:scale(0);opacity:0;transition:opacity 15ms linear 150ms,transform 180ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-fab--exited .mdc-fab__icon{transform:scale(0);transition:transform 135ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mat-mdc-fab,.mat-mdc-mini-fab{background-color:var(--mdc-fab-container-color);--mdc-fab-container-shape:50%;--mdc-fab-icon-size:24px}.mat-mdc-fab .mdc-fab__icon,.mat-mdc-mini-fab .mdc-fab__icon{width:var(--mdc-fab-icon-size);height:var(--mdc-fab-icon-size);font-size:var(--mdc-fab-icon-size)}.mat-mdc-fab:not(:disabled) .mdc-fab__icon,.mat-mdc-mini-fab:not(:disabled) .mdc-fab__icon{color:var(--mdc-fab-icon-color)}.mat-mdc-fab:not(.mdc-fab--extended),.mat-mdc-mini-fab:not(.mdc-fab--extended){border-radius:var(--mdc-fab-container-shape)}.mat-mdc-fab:not(.mdc-fab--extended) .mdc-fab__ripple,.mat-mdc-mini-fab:not(.mdc-fab--extended) .mdc-fab__ripple{border-radius:var(--mdc-fab-container-shape)}.mat-mdc-extended-fab{font-family:var(--mdc-extended-fab-label-text-font);font-size:var(--mdc-extended-fab-label-text-size);font-weight:var(--mdc-extended-fab-label-text-weight);letter-spacing:var(--mdc-extended-fab-label-text-tracking)}.mat-mdc-fab,.mat-mdc-mini-fab{-webkit-tap-highlight-color:rgba(0,0,0,0);box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);color:var(--mat-mdc-fab-color, inherit);flex-shrink:0}.mat-mdc-fab .mat-mdc-button-ripple,.mat-mdc-fab .mat-mdc-button-persistent-ripple,.mat-mdc-fab .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab .mat-mdc-button-ripple,.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple,.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab .mat-mdc-button-ripple,.mat-mdc-mini-fab .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0;background-color:var(--mat-mdc-button-persistent-ripple-color)}.mat-mdc-fab .mat-ripple-element,.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-mdc-button-ripple-color)}.mat-mdc-fab .mdc-button__label,.mat-mdc-mini-fab .mdc-button__label{z-index:1}.mat-mdc-fab .mat-mdc-focus-indicator,.mat-mdc-mini-fab .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab:focus .mat-mdc-focus-indicator::before,.mat-mdc-mini-fab:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-fab .mat-mdc-button-touch-target,.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-fab._mat-animation-noopable,.mat-mdc-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab:hover,.mat-mdc-fab:focus,.mat-mdc-mini-fab:hover,.mat-mdc-mini-fab:focus{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-mdc-fab:active,.mat-mdc-fab:focus:active,.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-mdc-fab[disabled],.mat-mdc-mini-fab[disabled]{cursor:default;pointer-events:none;box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-fab:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-mini-fab:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}.mat-mdc-fab .mat-icon,.mat-mdc-fab .material-icons,.mat-mdc-mini-fab .mat-icon,.mat-mdc-mini-fab .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab .mat-mdc-focus-indicator::before,.mat-mdc-mini-fab .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:calc(12px - 20px);margin-right:12px}[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons,.mat-mdc-extended-fab>.mat-icon[dir=rtl],.mat-mdc-extended-fab>.material-icons[dir=rtl]{margin-left:12px;margin-right:calc(12px - 20px)}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons{margin-left:12px;margin-right:calc(12px - 20px)}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-extended-fab .mdc-button__label+.material-icons[dir=rtl]{margin-left:calc(12px - 20px);margin-right:12px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatFabButton;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material Design mini floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://material.io/components/buttons-floating-action-button/\n */\nlet MatMiniFabButton = /*#__PURE__*/(() => {\n  class MatMiniFabButton extends MatButtonBase {\n    constructor(elementRef, platform, ngZone, animationMode, _options) {\n      super(elementRef, platform, ngZone, animationMode);\n      this._options = _options;\n      this._isFab = true;\n      this._options = this._options || defaults;\n      this.color = this.defaultColor = this._options.color || defaults.color;\n    }\n    static {\n      this.ɵfac = function MatMiniFabButton_Factory(t) {\n        return new (t || MatMiniFabButton)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_FAB_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatMiniFabButton,\n        selectors: [[\"button\", \"mat-mini-fab\", \"\"]],\n        hostVars: 7,\n        hostBindings: function MatMiniFabButton_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\"\n        },\n        exportAs: [\"matButton\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c6,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatMiniFabButton_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [_c7],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatMiniFabButton;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material Design floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n *\n * The `MatFabAnchor` class has two appearances: normal and extended.\n */\nlet MatFabAnchor = /*#__PURE__*/(() => {\n  class MatFabAnchor extends MatAnchor {\n    get extended() {\n      return this._extended;\n    }\n    set extended(value) {\n      this._extended = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, platform, ngZone, animationMode, _options) {\n      super(elementRef, platform, ngZone, animationMode);\n      this._options = _options;\n      this._isFab = true;\n      this._options = this._options || defaults;\n      this.color = this.defaultColor = this._options.color || defaults.color;\n    }\n    static {\n      this.ɵfac = function MatFabAnchor_Factory(t) {\n        return new (t || MatFabAnchor)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_FAB_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatFabAnchor,\n        selectors: [[\"a\", \"mat-fab\", \"\"]],\n        hostVars: 13,\n        hostBindings: function MatFabAnchor_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString());\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true)(\"mdc-fab--extended\", ctx.extended)(\"mat-mdc-extended-fab\", ctx.extended);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\",\n          tabIndex: \"tabIndex\",\n          extended: \"extended\"\n        },\n        exportAs: [\"matButton\", \"matAnchor\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c5,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatFabAnchor_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [_c7],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatFabAnchor;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material Design mini floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://material.io/components/buttons-floating-action-button/\n */\nlet MatMiniFabAnchor = /*#__PURE__*/(() => {\n  class MatMiniFabAnchor extends MatAnchor {\n    constructor(elementRef, platform, ngZone, animationMode, _options) {\n      super(elementRef, platform, ngZone, animationMode);\n      this._options = _options;\n      this._isFab = true;\n      this._options = this._options || defaults;\n      this.color = this.defaultColor = this._options.color || defaults.color;\n    }\n    static {\n      this.ɵfac = function MatMiniFabAnchor_Factory(t) {\n        return new (t || MatMiniFabAnchor)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_FAB_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatMiniFabAnchor,\n        selectors: [[\"a\", \"mat-mini-fab\", \"\"]],\n        hostVars: 9,\n        hostBindings: function MatMiniFabAnchor_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString());\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\",\n          tabIndex: \"tabIndex\"\n        },\n        exportAs: [\"matButton\", \"matAnchor\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c6,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatMiniFabAnchor_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [_c7],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatMiniFabAnchor;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nlet MatIconButton = /*#__PURE__*/(() => {\n  class MatIconButton extends MatButtonBase {\n    constructor(elementRef, platform, ngZone, animationMode) {\n      super(elementRef, platform, ngZone, animationMode);\n      this._rippleLoader.configureRipple(this._elementRef.nativeElement, {\n        centered: true\n      });\n    }\n    static {\n      this.ɵfac = function MatIconButton_Factory(t) {\n        return new (t || MatIconButton)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatIconButton,\n        selectors: [[\"button\", \"mat-icon-button\", \"\"]],\n        hostVars: 7,\n        hostBindings: function MatIconButton_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\"\n        },\n        exportAs: [\"matButton\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c8,\n        ngContentSelectors: _c9,\n        decls: 4,\n        vars: 0,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatIconButton_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n          }\n        },\n        styles: [\".mdc-icon-button{display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;user-select:none;z-index:0;overflow:visible}.mdc-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}@media screen and (forced-colors: active){.mdc-icon-button.mdc-ripple-upgraded--background-focused .mdc-icon-button__focus-ring,.mdc-icon-button:not(.mdc-ripple-upgraded):focus .mdc-icon-button__focus-ring{display:block}}.mdc-icon-button:disabled{cursor:default;pointer-events:none}.mdc-icon-button[hidden]{display:none}.mdc-icon-button--display-flex{align-items:center;display:inline-flex;justify-content:center}.mdc-icon-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%;display:none}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring{border-color:CanvasText}}.mdc-icon-button__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring::after{border-color:CanvasText}}.mdc-icon-button__icon{display:inline-block}.mdc-icon-button__icon.mdc-icon-button__icon--on{display:none}.mdc-icon-button--on .mdc-icon-button__icon{display:none}.mdc-icon-button--on .mdc-icon-button__icon.mdc-icon-button__icon--on{display:inline-block}.mdc-icon-button__link{height:100%;left:0;outline:none;position:absolute;top:0;width:100%}.mat-mdc-icon-button{height:var(--mdc-icon-button-state-layer-size);width:var(--mdc-icon-button-state-layer-size);color:var(--mdc-icon-button-icon-color);--mdc-icon-button-state-layer-size:48px;--mdc-icon-button-icon-size:24px;--mdc-icon-button-disabled-icon-color:black;--mdc-icon-button-disabled-icon-opacity:0.38}.mat-mdc-icon-button .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button svg,.mat-mdc-icon-button img{width:var(--mdc-icon-button-icon-size);height:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button:disabled{opacity:var(--mdc-icon-button-disabled-icon-opacity)}.mat-mdc-icon-button:disabled{color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button{padding:12px;font-size:var(--mdc-icon-button-icon-size);border-radius:50%;flex-shrink:0;text-align:center;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button svg{vertical-align:baseline}.mat-mdc-icon-button[disabled]{cursor:default;pointer-events:none;opacity:1}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0;background-color:var(--mat-mdc-button-persistent-ripple-color)}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-mdc-button-ripple-color)}.mat-mdc-icon-button .mdc-button__label{z-index:1}.mat-mdc-icon-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\", _c4],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatIconButton;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nlet MatIconAnchor = /*#__PURE__*/(() => {\n  class MatIconAnchor extends MatAnchorBase {\n    constructor(elementRef, platform, ngZone, animationMode) {\n      super(elementRef, platform, ngZone, animationMode);\n    }\n    static {\n      this.ɵfac = function MatIconAnchor_Factory(t) {\n        return new (t || MatIconAnchor)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatIconAnchor,\n        selectors: [[\"a\", \"mat-icon-button\", \"\"]],\n        hostVars: 9,\n        hostBindings: function MatIconAnchor_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString());\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          disableRipple: \"disableRipple\",\n          color: \"color\",\n          tabIndex: \"tabIndex\"\n        },\n        exportAs: [\"matButton\", \"matAnchor\"],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        attrs: _c8,\n        ngContentSelectors: _c2,\n        decls: 7,\n        vars: 4,\n        consts: [[1, \"mat-mdc-button-persistent-ripple\"], [1, \"mdc-button__label\"], [1, \"mat-mdc-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n        template: function MatIconAnchor_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelement(0, \"span\", 0);\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementStart(2, \"span\", 1);\n            i0.ɵɵprojection(3, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(4, 2);\n            i0.ɵɵelement(5, \"span\", 2)(6, \"span\", 3);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mdc-button__ripple\", !ctx._isFab)(\"mdc-fab__ripple\", ctx._isFab);\n          }\n        },\n        styles: [_c10, _c4],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatIconAnchor;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatButtonModule = /*#__PURE__*/(() => {\n  class MatButtonModule {\n    static {\n      this.ɵfac = function MatButtonModule_Factory(t) {\n        return new (t || MatButtonModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatButtonModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, MatRippleModule, MatCommonModule]\n      });\n    }\n  }\n  return MatButtonModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_FAB_DEFAULT_OPTIONS, MAT_FAB_DEFAULT_OPTIONS_FACTORY, MatAnchor, MatButton, MatButtonModule, MatFabAnchor, MatFabButton, MatIconAnchor, MatIconButton, MatMiniFabAnchor, MatMiniFabButton };\n//# sourceMappingURL=button.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}