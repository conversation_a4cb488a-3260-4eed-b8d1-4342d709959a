{"ast": null, "code": "import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nexport let Observable = /*#__PURE__*/(() => {\n  class Observable {\n    constructor(subscribe) {\n      if (subscribe) {\n        this._subscribe = subscribe;\n      }\n    }\n    lift(operator) {\n      const observable = new Observable();\n      observable.source = this;\n      observable.operator = operator;\n      return observable;\n    }\n    subscribe(observerOrNext, error, complete) {\n      const subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n      errorContext(() => {\n        const {\n          operator,\n          source\n        } = this;\n        subscriber.add(operator ? operator.call(subscriber, source) : source ? this._subscribe(subscriber) : this._trySubscribe(subscriber));\n      });\n      return subscriber;\n    }\n    _trySubscribe(sink) {\n      try {\n        return this._subscribe(sink);\n      } catch (err) {\n        sink.error(err);\n      }\n    }\n    forEach(next, promiseCtor) {\n      promiseCtor = getPromiseCtor(promiseCtor);\n      return new promiseCtor((resolve, reject) => {\n        const subscriber = new SafeSubscriber({\n          next: value => {\n            try {\n              next(value);\n            } catch (err) {\n              reject(err);\n              subscriber.unsubscribe();\n            }\n          },\n          error: reject,\n          complete: resolve\n        });\n        this.subscribe(subscriber);\n      });\n    }\n    _subscribe(subscriber) {\n      var _a;\n      return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n    }\n    [Symbol_observable]() {\n      return this;\n    }\n    pipe(...operations) {\n      return pipeFromArray(operations)(this);\n    }\n    toPromise(promiseCtor) {\n      promiseCtor = getPromiseCtor(promiseCtor);\n      return new promiseCtor((resolve, reject) => {\n        let value;\n        this.subscribe(x => value = x, err => reject(err), () => resolve(value));\n      });\n    }\n  }\n  Observable.create = subscribe => {\n    return new Observable(subscribe);\n  };\n  return Observable;\n})();\nfunction getPromiseCtor(promiseCtor) {\n  var _a;\n  return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n  return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n  return value && value instanceof Subscriber || isObserver(value) && isSubscription(value);\n}\n//# sourceMappingURL=Observable.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}