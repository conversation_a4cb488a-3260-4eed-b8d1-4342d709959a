{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let EmployerGuard = /*#__PURE__*/(() => {\n  class EmployerGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      if (this.authService.isEmployer()) {\n        return true;\n      } else {\n        this.router.navigate(['/jobs']);\n        return false;\n      }\n    }\n    static {\n      this.ɵfac = function EmployerGuard_Factory(t) {\n        return new (t || EmployerGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EmployerGuard,\n        factory: EmployerGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EmployerGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}