{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./components/header/header.component\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor() {\n      this.title = 'client';\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 3,\n        vars: 0,\n        consts: [[1, \"container\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-header\");\n            i0.ɵɵelementStart(1, \"div\", 0);\n            i0.ɵɵelement(2, \"router-outlet\");\n            i0.ɵɵelementEnd();\n          }\n        },\n        dependencies: [i1.RouterOutlet, i2.HeaderComponent],\n        styles: [\".container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;min-height:calc(100vh - 64px)}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}