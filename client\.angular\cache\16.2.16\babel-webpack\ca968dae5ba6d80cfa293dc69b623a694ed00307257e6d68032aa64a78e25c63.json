{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport let ProfileService = /*#__PURE__*/(() => {\n  class ProfileService {\n    constructor(apiService) {\n      this.apiService = apiService;\n    }\n    /**\n     * Get user profile\n     * @returns Observable of profile response\n     */\n    getProfile() {\n      return this.apiService.get('profile');\n    }\n    /**\n     * Update user profile\n     * @param profileData Profile update request\n     * @returns Observable of profile response\n     */\n    updateProfile(profileData) {\n      return this.apiService.put('profile', profileData);\n    }\n    static {\n      this.ɵfac = function ProfileService_Factory(t) {\n        return new (t || ProfileService)(i0.ɵɵinject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProfileService,\n        factory: ProfileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProfileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}