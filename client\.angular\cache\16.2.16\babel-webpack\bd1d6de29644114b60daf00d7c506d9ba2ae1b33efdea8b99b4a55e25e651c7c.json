{"ast": null, "code": "import { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nexport class AsyncAction extends Action {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.pending = false;\n  }\n  schedule(state, delay = 0) {\n    var _a;\n    if (this.closed) {\n      return this;\n    }\n    this.state = state;\n    const id = this.id;\n    const scheduler = this.scheduler;\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n    this.pending = true;\n    this.delay = delay;\n    this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n    return this;\n  }\n  requestAsyncId(scheduler, _id, delay = 0) {\n    return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n  }\n  recycleAsyncId(_scheduler, id, delay = 0) {\n    if (delay != null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n    if (id != null) {\n      intervalProvider.clearInterval(id);\n    }\n    return undefined;\n  }\n  execute(state, delay) {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n    this.pending = false;\n    const error = this._execute(state, delay);\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  }\n  _execute(state, _delay) {\n    let errored = false;\n    let errorValue;\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      errorValue = e ? e : new Error('Scheduled action threw falsy error');\n    }\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  }\n  unsubscribe() {\n    if (!this.closed) {\n      const {\n        id,\n        scheduler\n      } = this;\n      const {\n        actions\n      } = scheduler;\n      this.work = this.state = this.scheduler = null;\n      this.pending = false;\n      arrRemove(actions, this);\n      if (id != null) {\n        this.id = this.recycleAsyncId(scheduler, id, null);\n      }\n      this.delay = null;\n      super.unsubscribe();\n    }\n  }\n}\n//# sourceMappingURL=AsyncAction.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}