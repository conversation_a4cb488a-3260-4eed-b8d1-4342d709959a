{"ast": null, "code": "import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n  var _a, _b;\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxWindowSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let windowRecords = [];\n    let restartOnClose = false;\n    const closeWindow = record => {\n      const {\n        window,\n        subs\n      } = record;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n    const startWindow = () => {\n      if (windowRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const window = new Subject();\n        const record = {\n          window,\n          subs,\n          seen: 0\n        };\n        windowRecords.push(record);\n        subscriber.next(window.asObservable());\n        executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n      }\n    };\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n    startWindow();\n    const loop = cb => windowRecords.slice().forEach(cb);\n    const terminate = cb => {\n      loop(({\n        window\n      }) => cb(window));\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      loop(record => {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, () => terminate(consumer => consumer.complete()), err => terminate(consumer => consumer.error(err))));\n    return () => {\n      windowRecords = null;\n    };\n  });\n}\n//# sourceMappingURL=windowTime.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}