{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let JobSeekerGuard = /*#__PURE__*/(() => {\n  class JobSeekerGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      if (this.authService.isJobSeeker()) {\n        return true;\n      } else {\n        this.router.navigate(['/jobs']);\n        return false;\n      }\n    }\n    static {\n      this.ɵfac = function JobSeekerGuard_Factory(t) {\n        return new (t || JobSeekerGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: JobSeekerGuard,\n        factory: JobSeekerGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return JobSeekerGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}