{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const UnsubscriptionError = createErrorClass(_super => function UnsubscriptionErrorImpl(errors) {\n  _super(this);\n  this.message = errors ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n  this.name = 'UnsubscriptionError';\n  this.errors = errors;\n});\n//# sourceMappingURL=UnsubscriptionError.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}