{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(apiService, router) {\n      this.apiService = apiService;\n      this.router = router;\n      this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n      this.currentUser = this.currentUserSubject.asObservable();\n      this.isAuthenticatedSubject = new BehaviorSubject(!!this.getUserFromStorage());\n      this.isAuthenticated = this.isAuthenticatedSubject.asObservable();\n    }\n    /**\n     * Get current user value\n     * @returns Current user\n     */\n    get currentUserValue() {\n      return this.currentUserSubject.value;\n    }\n    /**\n     * Login user\n     * @param loginRequest Login request\n     * @returns Observable of auth response\n     */\n    login(loginRequest) {\n      return this.apiService.post('auth/login', loginRequest, false).pipe(tap(response => {\n        if (response.success && response.data) {\n          this.setSession(response.data.token, response.data.user);\n        }\n      }));\n    }\n    /**\n     * Register user\n     * @param registerRequest Register request\n     * @returns Observable of auth response\n     */\n    register(registerRequest) {\n      return this.apiService.post('auth/register', registerRequest, false).pipe(tap(response => {\n        if (response.success && response.data) {\n          this.setSession(response.data.token, response.data.user);\n        }\n      }));\n    }\n    /**\n     * Get current user profile\n     * @returns Observable of auth response\n     */\n    getCurrentUser() {\n      return this.apiService.get('auth/me').pipe(tap(response => {\n        if (response.success && response.data) {\n          this.currentUserSubject.next(response.data.user);\n        }\n      }));\n    }\n    /**\n     * Logout user\n     */\n    logout() {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      this.currentUserSubject.next(null);\n      this.isAuthenticatedSubject.next(false);\n      this.router.navigate(['/login']);\n    }\n    /**\n     * Check if user is authenticated\n     * @returns True if user is authenticated\n     */\n    checkAuth() {\n      const token = localStorage.getItem('token');\n      return !!token;\n    }\n    /**\n     * Check if user is an employer\n     * @returns True if user is an employer\n     */\n    isEmployer() {\n      const user = this.currentUserValue;\n      return user?.role === 'employer';\n    }\n    /**\n     * Check if user is a job seeker\n     * @returns True if user is a job seeker\n     */\n    isJobSeeker() {\n      const user = this.currentUserValue;\n      return user?.role === 'job_seeker';\n    }\n    /**\n     * Set session data\n     * @param token JWT token\n     * @param user User data\n     */\n    setSession(token, user) {\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      this.currentUserSubject.next(user);\n      this.isAuthenticatedSubject.next(true);\n    }\n    /**\n     * Get user from storage\n     * @returns User from storage\n     */\n    getUserFromStorage() {\n      const userStr = localStorage.getItem('user');\n      if (userStr) {\n        try {\n          return JSON.parse(userStr);\n        } catch (e) {\n          return null;\n        }\n      }\n      return null;\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}