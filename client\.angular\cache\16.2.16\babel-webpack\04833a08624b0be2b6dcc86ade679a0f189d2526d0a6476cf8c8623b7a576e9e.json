{"ast": null, "code": "import { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/job.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/paginator\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/chips\";\nfunction JobListComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"a\", 18)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Post a Job \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction JobListComponent_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r6.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r6.label, \" \");\n  }\n}\nfunction JobListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"mat-spinner\", 21);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JobListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"search_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No jobs found. Try adjusting your filters.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction JobListComponent_div_33_mat_card_1_mat_chip_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(job_r8.salaryRange);\n  }\n}\nfunction JobListComponent_div_33_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 25);\n    i0.ɵɵlistener(\"click\", function JobListComponent_div_33_mat_card_1_Template_mat_card_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const job_r8 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.viewJobDetails(job_r8.id));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\")(5, \"div\", 26)(6, \"span\")(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\")(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"p\", 27);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 28)(19, \"mat-chip-set\")(20, \"mat-chip\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, JobListComponent_div_33_mat_card_1_mat_chip_23_Template, 2, 1, \"mat-chip\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"mat-card-actions\")(25, \"button\", 30);\n    i0.ɵɵtext(26, \"VIEW DETAILS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"span\", 31);\n    i0.ɵɵelementStart(28, \"span\", 32);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const job_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(job_r8.title);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", (job_r8.employer == null ? null : job_r8.employer.profile == null ? null : job_r8.employer.profile.companyName) || \"Company\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", job_r8.location, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind3(17, 8, job_r8.description, 0, 200), \"\", job_r8.description.length > 200 ? \"...\" : \"\", \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 12, job_r8.jobType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", job_r8.salaryRange);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Posted: \", i0.ɵɵpipeBind1(30, 14, job_r8.createdAt), \"\");\n  }\n}\nfunction JobListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, JobListComponent_div_33_mat_card_1_Template, 31, 16, \"mat-card\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.jobs);\n  }\n}\nconst _c0 = function () {\n  return [5, 10, 25, 50];\n};\nfunction JobListComponent_mat_paginator_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-paginator\", 33);\n    i0.ɵɵlistener(\"page\", function JobListComponent_mat_paginator_34_Template_mat_paginator_page_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"length\", ctx_r5.totalItems)(\"pageSize\", ctx_r5.pageSize)(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0))(\"pageIndex\", ctx_r5.currentPage);\n  }\n}\nexport let JobListComponent = /*#__PURE__*/(() => {\n  class JobListComponent {\n    constructor(jobService, authService, route, router, formBuilder, snackBar) {\n      this.jobService = jobService;\n      this.authService = authService;\n      this.route = route;\n      this.router = router;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.jobs = [];\n      this.loading = false;\n      this.totalItems = 0;\n      this.pageSize = 10;\n      this.currentPage = 0;\n      this.isEmployerView = false;\n      this.jobTypes = [{\n        value: 'full-time',\n        label: 'Full Time'\n      }, {\n        value: 'part-time',\n        label: 'Part Time'\n      }, {\n        value: 'contract',\n        label: 'Contract'\n      }, {\n        value: 'internship',\n        label: 'Internship'\n      }, {\n        value: 'remote',\n        label: 'Remote'\n      }];\n    }\n    ngOnInit() {\n      // Check if we're in employer mode (my-jobs)\n      this.route.data.subscribe(data => {\n        this.isEmployerView = data['employerJobs'] === true;\n      });\n      // Initialize filter form\n      this.filterForm = this.formBuilder.group({\n        search: [''],\n        location: [''],\n        jobType: ['']\n      });\n      // Subscribe to form changes with debounce\n      this.filterForm.valueChanges.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n        this.currentPage = 0;\n        this.loadJobs();\n      });\n      // Initial load\n      this.loadJobs();\n    }\n    loadJobs() {\n      this.loading = true;\n      const params = {\n        page: this.currentPage + 1,\n        limit: this.pageSize,\n        search: this.filterForm.get('search')?.value || '',\n        location: this.filterForm.get('location')?.value || '',\n        jobType: this.filterForm.get('jobType')?.value || ''\n      };\n      // Determine which service method to call based on view\n      const jobsObservable = this.isEmployerView ? this.jobService.getEmployerJobs(params) : this.jobService.getJobs(params);\n      jobsObservable.subscribe({\n        next: response => {\n          this.jobs = response.data.jobs;\n          this.totalItems = response.data.pagination.totalItems;\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Error loading jobs', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n        }\n      });\n    }\n    onPageChange(event) {\n      this.currentPage = event.pageIndex;\n      this.pageSize = event.pageSize;\n      this.loadJobs();\n    }\n    clearFilters() {\n      this.filterForm.reset();\n      this.currentPage = 0;\n      this.loadJobs();\n    }\n    viewJobDetails(jobId) {\n      this.router.navigate(['/jobs', jobId]);\n    }\n    isAuthenticated() {\n      return this.authService.checkAuth();\n    }\n    isEmployer() {\n      return this.authService.isEmployer();\n    }\n    isJobSeeker() {\n      return this.authService.isJobSeeker();\n    }\n    static {\n      this.ɵfac = function JobListComponent_Factory(t) {\n        return new (t || JobListComponent)(i0.ɵɵdirectiveInject(i1.JobService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: JobListComponent,\n        selectors: [[\"app-job-list\"]],\n        viewQuery: function JobListComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 35,\n        vars: 8,\n        consts: [[1, \"job-list-container\"], [1, \"job-list-header\"], [\"class\", \"action-button\", 4, \"ngIf\"], [1, \"filter-card\"], [1, \"filter-form\", 3, \"formGroup\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"search\", \"placeholder\", \"Job title, keywords...\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"location\", \"placeholder\", \"City, state, remote...\"], [\"matNativeControl\", \"\", \"formControlName\", \"jobType\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"job-cards\", 4, \"ngIf\"], [\"aria-label\", \"Select page\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\", 4, \"ngIf\"], [1, \"action-button\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/create-job\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"no-results\"], [1, \"job-cards\"], [\"class\", \"job-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"job-card\", 3, \"click\"], [1, \"job-subtitle\"], [1, \"job-description\"], [1, \"job-details\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"spacer\"], [1, \"job-date\"], [\"aria-label\", \"Select page\", 3, \"length\", \"pageSize\", \"pageSizeOptions\", \"pageIndex\", \"page\"]],\n        template: function JobListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, JobListComponent_div_4_Template, 5, 0, \"div\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"mat-card\", 3)(6, \"mat-card-content\")(7, \"form\", 4)(8, \"mat-form-field\", 5)(9, \"mat-label\");\n            i0.ɵɵtext(10, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"input\", 6);\n            i0.ɵɵelementStart(12, \"mat-icon\", 7);\n            i0.ɵɵtext(13, \"search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"mat-form-field\", 5)(15, \"mat-label\");\n            i0.ɵɵtext(16, \"Location\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"input\", 8);\n            i0.ɵɵelementStart(18, \"mat-icon\", 7);\n            i0.ɵɵtext(19, \"location_on\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"mat-form-field\", 5)(21, \"mat-label\");\n            i0.ɵɵtext(22, \"Job Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"select\", 9)(24, \"option\", 10);\n            i0.ɵɵtext(25, \"All Types\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(26, JobListComponent_option_26_Template, 2, 2, \"option\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function JobListComponent_Template_button_click_27_listener() {\n              return ctx.clearFilters();\n            });\n            i0.ɵɵelementStart(28, \"mat-icon\");\n            i0.ɵɵtext(29, \"clear\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \" Clear Filters \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(31, JobListComponent_div_31_Template, 2, 0, \"div\", 13);\n            i0.ɵɵtemplate(32, JobListComponent_div_32_Template, 5, 0, \"div\", 14);\n            i0.ɵɵtemplate(33, JobListComponent_div_33_Template, 2, 1, \"div\", 15);\n            i0.ɵɵtemplate(34, JobListComponent_mat_paginator_34_Template, 1, 5, \"mat-paginator\", 16);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.isEmployerView ? \"My Posted Jobs\" : \"Browse Jobs\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEmployer() && !ctx.isEmployerView);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"ngForOf\", ctx.jobTypes);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.jobs.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.jobs.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.totalItems > 0);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i3.RouterLink, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i7.MatAnchor, i7.MatButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatInput, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatPaginator, i12.MatIcon, i13.MatProgressSpinner, i14.MatChip, i14.MatChipSet, i6.SlicePipe, i6.TitleCasePipe, i6.DatePipe],\n        styles: [\".job-list-container[_ngcontent-%COMP%]{margin-bottom:30px}.job-list-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.job-list-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-weight:500}.filter-card[_ngcontent-%COMP%]{margin-bottom:20px}.filter-form[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;align-items:center}.filter-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.filter-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:56px;margin-top:-8px}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:40px 0}.no-results[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:40px 0;color:#666}.no-results[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;margin-bottom:16px}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px}.job-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;margin-bottom:20px}.job-card[_ngcontent-%COMP%]{cursor:pointer;transition:transform .2s,box-shadow .2s;height:100%;display:flex;flex-direction:column}.job-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 6px 12px #0000001a}.job-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{flex-grow:1}.job-subtitle[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-top:8px}.job-subtitle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center}.job-subtitle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;width:16px;margin-right:4px}.job-description[_ngcontent-%COMP%]{margin:16px 0;color:#555;line-height:1.5}.job-details[_ngcontent-%COMP%]{margin-top:16px}mat-card-actions[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px 16px}.spacer[_ngcontent-%COMP%]{flex:1}.job-date[_ngcontent-%COMP%]{font-size:12px;color:#666}@media (max-width: 768px){.job-list-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.filter-form[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filter-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%}.filter-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.job-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return JobListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}